#!/usr/bin/env python3
"""
Test script to verify the Python environment
"""

import sys
import os

print("🔍 Environment Test")
print("=" * 50)
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")
print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries

print("\n📦 Testing Package Imports")
print("=" * 50)

# Test basic imports
packages_to_test = [
    "os", "sys", "json", "csv", "datetime", "pathlib",
    "flask", "requests", "beautifulsoup4"
]

for package in packages_to_test:
    try:
        if package == "beautifulsoup4":
            import bs4
            print(f"✅ {package} (bs4): OK")
        else:
            __import__(package)
            print(f"✅ {package}: OK")
    except ImportError as e:
        print(f"❌ {package}: FAILED - {e}")

# Test problematic packages separately
print("\n🔬 Testing Problematic Packages")
print("=" * 50)

try:
    import numpy
    print(f"✅ numpy: OK - version {numpy.__version__}")
except ImportError as e:
    print(f"❌ numpy: FAILED - {e}")

try:
    import pandas
    print(f"✅ pandas: OK - version {pandas.__version__}")
except ImportError as e:
    print(f"❌ pandas: FAILED - {e}")

print("\n🎯 Environment Summary")
print("=" * 50)
print("If numpy/pandas failed, the admin panel won't work.")
print("But we can create a simplified version without them.")
