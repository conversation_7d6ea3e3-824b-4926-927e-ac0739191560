<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🔧 MCP Configuration Generator</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
      }

      .header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        padding: 1rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      }

      .header h1 {
        text-align: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 0.5rem;
      }

      .subtitle {
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
        font-size: 1rem;
      }

      .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 1rem;
      }

      .config-generator {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        margin-bottom: 2rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #333;
      }

      .form-group input,
      .form-group select {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
      }

      .form-group input:focus,
      .form-group select:focus {
        outline: none;
        border-color: #667eea;
      }

      .btn {
        background: #000000;
        color: white;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .btn:hover {
        background: #000000;
        transform: translateY(-2px);
      }

      .config-output {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .config-section {
        margin-bottom: 2rem;
      }

      .config-section h3 {
        color: #333;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #667eea;
      }

      .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 1rem;
        border-radius: 8px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        font-size: 0.9rem;
        position: relative;
        overflow-x: auto;
      }

      .copy-btn {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: #4a5568;
        color: white;
        border: none;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        cursor: pointer;
        transition: background 0.3s ease;
      }

      .copy-btn:hover {
        background: #2d3748;
      }

      .copy-btn.copied {
        background: #27ae60;
      }

      .tabs {
        display: flex;
        margin-bottom: 1rem;
        border-bottom: 1px solid #e1e5e9;
      }

      .tab {
        padding: 0.75rem 1.5rem;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 1rem;
        color: #666;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;
      }

      .tab.active {
        color: #667eea;
        border-bottom-color: #667eea;
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      .installation-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
      }

      .method-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
      }

      .method-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
      }

      @media (max-width: 768px) {
        .container {
          padding: 0 0.5rem;
        }

        .installation-methods {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🔧 MCP Configuration Generator</h1>
      <div class="subtitle">
        Generate configuration files for your MCP servers
      </div>
    </div>

    <div class="container">
      <div class="config-generator">
        <h2>📝 Server Configuration</h2>
        <form id="configForm">
          <div class="form-group">
            <label for="serverName">Server Name:</label>
            <input
              type="text"
              id="serverName"
              placeholder="e.g., my-mcp-server"
              required
            />
          </div>

          <div class="form-group">
            <label for="packageName">Package Name:</label>
            <input
              type="text"
              id="packageName"
              placeholder="e.g., @myorg/mcp-server-package"
            />
          </div>

          <div class="form-group">
            <label for="installMethod">Installation Method:</label>
            <select id="installMethod">
              <option value="npm">NPM Package</option>
              <option value="docker">Docker Container</option>
              <option value="manual">Manual Installation</option>
            </select>
          </div>

          <div class="form-group">
            <label for="envVars">Environment Variables (one per line):</label>
            <textarea
              id="envVars"
              rows="4"
              placeholder="API_KEY=your-api-key&#10;BASE_URL=https://api.example.com"
              style="
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-family: monospace;
              "
            ></textarea>
          </div>

          <button type="submit" class="btn">🚀 Generate Configuration</button>
        </form>
      </div>

      <div class="config-output" id="configOutput" style="display: none">
        <h2>📋 Generated Configurations</h2>

        <div class="tabs">
          <button class="tab active" onclick="showTab('claude-desktop')">
            🤖 Claude Desktop
          </button>
          <button class="tab" onclick="showTab('cursor')">🎯 Cursor</button>
          <button class="tab" onclick="showTab('vscode')">📝 VS Code</button>
          <button class="tab" onclick="showTab('windsurf')">🏄 Windsurf</button>
          <button class="tab" onclick="showTab('installation')">
            📦 Installation
          </button>
        </div>

        <div id="claude-desktop" class="tab-content active">
          <div class="config-section">
            <h3>Claude Desktop Configuration</h3>
            <p>
              Add this to your <code>claude_desktop_config.json</code> file:
            </p>
            <div class="code-block">
              <button
                class="copy-btn"
                onclick="copyToClipboard('claude-config')"
              >
                📋 Copy
              </button>
              <pre id="claude-config"></pre>
            </div>
          </div>
        </div>

        <div id="cursor" class="tab-content">
          <div class="config-section">
            <h3>Cursor Configuration</h3>
            <p>Add this to your Cursor MCP configuration:</p>
            <div class="code-block">
              <button
                class="copy-btn"
                onclick="copyToClipboard('cursor-config')"
              >
                📋 Copy
              </button>
              <pre id="cursor-config"></pre>
            </div>
          </div>
        </div>

        <div id="vscode" class="tab-content">
          <div class="config-section">
            <h3>VS Code Configuration</h3>
            <p>Add this to your <code>.vscode/mcp.json</code> file:</p>
            <div class="code-block">
              <button
                class="copy-btn"
                onclick="copyToClipboard('vscode-config')"
              >
                📋 Copy
              </button>
              <pre id="vscode-config"></pre>
            </div>
          </div>
        </div>

        <div id="windsurf" class="tab-content">
          <div class="config-section">
            <h3>Windsurf Configuration</h3>
            <p>Add this to your Windsurf MCP configuration:</p>
            <div class="code-block">
              <button
                class="copy-btn"
                onclick="copyToClipboard('windsurf-config')"
              >
                📋 Copy
              </button>
              <pre id="windsurf-config"></pre>
            </div>
          </div>
        </div>

        <div id="installation" class="tab-content">
          <div class="config-section">
            <h3>Installation Instructions</h3>
            <div class="installation-methods">
              <div class="method-card">
                <div class="method-title">📦 NPM Installation</div>
                <div class="code-block">
                  <button
                    class="copy-btn"
                    onclick="copyToClipboard('npm-install')"
                  >
                    📋 Copy
                  </button>
                  <pre id="npm-install"></pre>
                </div>
              </div>
              <div class="method-card">
                <div class="method-title">🐳 Docker Installation</div>
                <div class="code-block">
                  <button
                    class="copy-btn"
                    onclick="copyToClipboard('docker-install')"
                  >
                    📋 Copy
                  </button>
                  <pre id="docker-install"></pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      document
        .getElementById("configForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          generateConfigurations();
        });

      function generateConfigurations() {
        const serverName = document.getElementById("serverName").value;
        const packageName =
          document.getElementById("packageName").value || serverName;
        const installMethod = document.getElementById("installMethod").value;
        const envVarsText = document.getElementById("envVars").value;

        // Parse environment variables
        const envVars = {};
        if (envVarsText.trim()) {
          envVarsText.split("\n").forEach((line) => {
            const [key, value] = line.split("=");
            if (key && value) {
              envVars[key.trim()] = value.trim();
            }
          });
        }

        // Generate configurations
        generateClaudeDesktopConfig(
          serverName,
          packageName,
          installMethod,
          envVars
        );
        generateCursorConfig(serverName, packageName, installMethod, envVars);
        generateVSCodeConfig(serverName, packageName, installMethod, envVars);
        generateWindsurfConfig(serverName, packageName, installMethod, envVars);
        generateInstallationInstructions(packageName, installMethod);

        // Show output
        document.getElementById("configOutput").style.display = "block";
        document
          .getElementById("configOutput")
          .scrollIntoView({ behavior: "smooth" });
      }

      function generateClaudeDesktopConfig(
        serverName,
        packageName,
        installMethod,
        envVars
      ) {
        let config = {
          mcpServers: {},
        };

        if (installMethod === "npm") {
          config.mcpServers[serverName] = {
            command: "npx",
            args: ["-y", packageName],
          };
        } else if (installMethod === "docker") {
          config.mcpServers[serverName] = {
            command: "docker",
            args: ["run", "--rm", "-i", `${serverName}:latest`],
          };
        } else {
          config.mcpServers[serverName] = {
            command: "node",
            args: [`/path/to/${serverName}/index.js`],
          };
        }

        if (Object.keys(envVars).length > 0) {
          config.mcpServers[serverName].env = envVars;
        }

        document.getElementById("claude-config").textContent = JSON.stringify(
          config,
          null,
          2
        );
      }

      function generateCursorConfig(
        serverName,
        packageName,
        installMethod,
        envVars
      ) {
        // Similar to Claude Desktop
        generateClaudeDesktopConfig(
          serverName,
          packageName,
          installMethod,
          envVars
        );
        document.getElementById("cursor-config").textContent =
          document.getElementById("claude-config").textContent;
      }

      function generateVSCodeConfig(
        serverName,
        packageName,
        installMethod,
        envVars
      ) {
        let config = {
          servers: {},
        };

        if (installMethod === "npm") {
          config.servers[serverName] = {
            command: "npx",
            args: ["-y", packageName],
          };
        } else if (installMethod === "docker") {
          config.servers[serverName] = {
            command: "docker",
            args: ["run", "--rm", "-i", `${serverName}:latest`],
          };
        } else {
          config.servers[serverName] = {
            command: "node",
            args: [`/path/to/${serverName}/index.js`],
          };
        }

        if (Object.keys(envVars).length > 0) {
          config.servers[serverName].env = envVars;
        }

        document.getElementById("vscode-config").textContent = JSON.stringify(
          config,
          null,
          2
        );
      }

      function generateWindsurfConfig(
        serverName,
        packageName,
        installMethod,
        envVars
      ) {
        // Similar to Claude Desktop
        generateClaudeDesktopConfig(
          serverName,
          packageName,
          installMethod,
          envVars
        );
        document.getElementById("windsurf-config").textContent =
          document.getElementById("claude-config").textContent;
      }

      function generateInstallationInstructions(packageName, installMethod) {
        let npmInstall = "";
        let dockerInstall = "";

        if (installMethod === "npm") {
          npmInstall = `# Install the package globally
npm install -g ${packageName}

# Or run directly with npx
npx ${packageName}`;
        } else {
          npmInstall = `# Install via NPM
npm install ${packageName}`;
        }

        dockerInstall = `# Pull the Docker image
docker pull ${packageName}

# Run the container
docker run --rm -i ${packageName}`;

        document.getElementById("npm-install").textContent = npmInstall;
        document.getElementById("docker-install").textContent = dockerInstall;
      }

      function showTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll(".tab-content").forEach((content) => {
          content.classList.remove("active");
        });

        // Remove active class from all tabs
        document.querySelectorAll(".tab").forEach((tab) => {
          tab.classList.remove("active");
        });

        // Show selected tab content
        document.getElementById(tabName).classList.add("active");

        // Add active class to clicked tab
        event.target.classList.add("active");
      }

      function copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        const text = element.textContent;

        navigator.clipboard
          .writeText(text)
          .then(() => {
            const button = element.parentElement.querySelector(".copy-btn");
            const originalText = button.textContent;
            button.textContent = "✅ Copied!";
            button.classList.add("copied");

            setTimeout(() => {
              button.textContent = originalText;
              button.classList.remove("copied");
            }, 2000);
          })
          .catch((err) => {
            console.error("Failed to copy text: ", err);
            alert("Failed to copy to clipboard");
          });
      }
    </script>
  </body>
</html>
