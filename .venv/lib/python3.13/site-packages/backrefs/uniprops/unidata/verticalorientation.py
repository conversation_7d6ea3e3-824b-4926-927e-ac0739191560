"""Unicode Properties from Unicode version 15.1.0 (autogen)."""
from __future__ import annotations

unicode_vertical_orientation: dict[str, str] = {
    "^r": "\xa7\xa9\xae\xb1\xbc-\xbe\xd7\xf7\u02ea-\u02eb\u1100-\u11ff\u1401-\u167f\u18b0-\u18ff\u2016\u2020-\u2021\u2030-\u2031\u203b-\u203c\u2042\u2047-\u2049\u2051\u2065\u20dd-\u20e0\u20e2-\u20e4\u2100-\u2101\u2103-\u2109\u210f\u2113-\u2114\u2116-\u2117\u211e-\u2123\u2125\u2127\u2129\u212e\u2135-\u213f\u2145-\u214a\u214c-\u214d\u214f-\u2189\u218c-\u218f\u221e\u2234-\u2235\u2300-\u2307\u230c-\u231f\u2324-\u232b\u237d-\u239a\u23be-\u23cd\u23cf\u23d1-\u23db\u23e2-\u2422\u2424-\u24ff\u25a0-\u2619\u2620-\u2767\u2776-\u2793\u2b12-\u2b2f\u2b50-\u2b59\u2b97\u2bb8-\u2bd1\u2bd3-\u2beb\u2bf0-\u2bff\u2e50-\u2e51\u2e80-\ua4cf\ua960-\ua97f\uac00-\ud7ff\ue000-\ufaff\ufe10-\ufe1f\ufe30-\ufe48\ufe50-\ufe57\ufe59-\ufe62\ufe67-\ufe6f\uff01-\uff0c\uff0e-\uff1b\uff1f-\uff60\uffe0-\uffe7\ufff0-\ufff8\ufffc-\ufffd\U00010980-\U0001099f\U00011580-\U000115ff\U00011a00-\U00011abf\U00013000-\U0001345f\U00014400-\U0001467f\U00016fe0-\U00018d7f\U0001aff0-\U0001b2ff\U0001cf00-\U0001cfcf\U0001d000-\U0001d1ff\U0001d2e0-\U0001d37f\U0001d800-\U0001daaf\U0001f000-\U0001f7ff\U0001f900-\U0001faff\U00020000-\U0002fffd\U00030000-\U0003fffd\U000f0000-\U000ffffd\U00100000-\U0010fffd",
    "^tr": "\x00-\u2328\u232b-\u3007\u3012-\u3013\u3020-\u302f\u3031-\u309f\u30a1-\u30fb\u30fd-\ufe58\ufe5f-\uff07\uff0a-\uff19\uff1c-\uff3a\uff3c\uff3e\uff40-\uff5a\uff61-\uffe2\uffe4-\U0010ffff",
    "^tu": "\x00-\u3000\u3003-\u3040\u3042\u3044\u3046\u3048\u304a-\u3062\u3064-\u3082\u3084\u3086\u3088-\u308d\u308f-\u3094\u3097-\u309a\u309d-\u30a0\u30a2\u30a4\u30a6\u30a8\u30aa-\u30c2\u30c4-\u30e2\u30e4\u30e6\u30e8-\u30ed\u30ef-\u30f4\u30f7-\u3126\u3128-\u31ef\u3200-\u32fe\u3358-\u337a\u3380-\ufe4f\ufe53-\uff00\uff02-\uff0b\uff0d\uff0f-\uff1e\uff20-\U0001f1ff\U0001f202-\U0010ffff",
    "^u": "\x00-\xa6\xa8\xaa-\xad\xaf-\xb0\xb2-\xbb\xbf-\xd6\xd8-\xf6\xf8-\u02e9\u02ec-\u10ff\u1200-\u1400\u1680-\u18af\u1900-\u2015\u2017-\u201f\u2022-\u202f\u2032-\u203a\u203d-\u2041\u2043-\u2046\u204a-\u2050\u2052-\u2064\u2066-\u20dc\u20e1\u20e5-\u20ff\u2102\u210a-\u210e\u2110-\u2112\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u212d\u212f-\u2134\u2140-\u2144\u214b\u214e\u218a-\u218b\u2190-\u221d\u221f-\u2233\u2236-\u22ff\u2308-\u230b\u2320-\u2323\u2329-\u232a\u232c-\u237c\u239b-\u23bd\u23ce\u23d0\u23dc-\u23e1\u2423\u2500-\u259f\u261a-\u261f\u2768-\u2775\u2794-\u2b11\u2b30-\u2b4f\u2b5a-\u2b96\u2b98-\u2bb7\u2bd2\u2bec-\u2bef\u2c00-\u2e4f\u2e52-\u2e7f\u3001-\u3002\u3008-\u3011\u3014-\u301f\u3030\u3041\u3043\u3045\u3047\u3049\u3063\u3083\u3085\u3087\u308e\u3095-\u3096\u309b-\u309c\u30a0-\u30a1\u30a3\u30a5\u30a7\u30a9\u30c3\u30e3\u30e5\u30e7\u30ee\u30f5-\u30f6\u30fc\u3127\u31f0-\u31ff\u32ff-\u3357\u337b-\u337f\ua4d0-\ua95f\ua980-\uabff\ud800-\udfff\ufb00-\ufe0f\ufe20-\ufe2f\ufe49-\ufe52\ufe58-\ufe5e\ufe63-\ufe66\ufe70-\uff01\uff08-\uff09\uff0c-\uff0e\uff1a-\uff1f\uff3b\uff3d\uff3f\uff5b-\uffdf\uffe3\uffe8-\uffef\ufff9-\ufffb\ufffe-\U0001097f\U000109a0-\U0001157f\U00011600-\U000119ff\U00011ac0-\U00012fff\U00013460-\U000143ff\U00014680-\U00016fdf\U00018d80-\U0001afef\U0001b300-\U0001ceff\U0001cfd0-\U0001cfff\U0001d200-\U0001d2df\U0001d380-\U0001d7ff\U0001dab0-\U0001efff\U0001f200-\U0001f201\U0001f800-\U0001f8ff\U0001fb00-\U0001ffff\U0002fffe-\U0002ffff\U0003fffe-\U000effff\U000ffffe-\U000fffff\U0010fffe-\U0010ffff",
    "r": "\x00-\xa6\xa8\xaa-\xad\xaf-\xb0\xb2-\xbb\xbf-\xd6\xd8-\xf6\xf8-\u02e9\u02ec-\u10ff\u1200-\u1400\u1680-\u18af\u1900-\u2015\u2017-\u201f\u2022-\u202f\u2032-\u203a\u203d-\u2041\u2043-\u2046\u204a-\u2050\u2052-\u2064\u2066-\u20dc\u20e1\u20e5-\u20ff\u2102\u210a-\u210e\u2110-\u2112\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u212d\u212f-\u2134\u2140-\u2144\u214b\u214e\u218a-\u218b\u2190-\u221d\u221f-\u2233\u2236-\u22ff\u2308-\u230b\u2320-\u2323\u232c-\u237c\u239b-\u23bd\u23ce\u23d0\u23dc-\u23e1\u2423\u2500-\u259f\u261a-\u261f\u2768-\u2775\u2794-\u2b11\u2b30-\u2b4f\u2b5a-\u2b96\u2b98-\u2bb7\u2bd2\u2bec-\u2bef\u2c00-\u2e4f\u2e52-\u2e7f\ua4d0-\ua95f\ua980-\uabff\ud800-\udfff\ufb00-\ufe0f\ufe20-\ufe2f\ufe49-\ufe4f\ufe58\ufe63-\ufe66\ufe70-\uff00\uff0d\uff1c-\uff1e\uff61-\uffdf\uffe8-\uffef\ufff9-\ufffb\ufffe-\U0001097f\U000109a0-\U0001157f\U00011600-\U000119ff\U00011ac0-\U00012fff\U00013460-\U000143ff\U00014680-\U00016fdf\U00018d80-\U0001afef\U0001b300-\U0001ceff\U0001cfd0-\U0001cfff\U0001d200-\U0001d2df\U0001d380-\U0001d7ff\U0001dab0-\U0001efff\U0001f800-\U0001f8ff\U0001fb00-\U0001ffff\U0002fffe-\U0002ffff\U0003fffe-\U000effff\U000ffffe-\U000fffff\U0010fffe-\U0010ffff",
    "tr": "\u2329-\u232a\u3008-\u3011\u3014-\u301f\u3030\u30a0\u30fc\ufe59-\ufe5e\uff08-\uff09\uff1a-\uff1b\uff3b\uff3d\uff3f\uff5b-\uff60\uffe3",
    "tu": "\u3001-\u3002\u3041\u3043\u3045\u3047\u3049\u3063\u3083\u3085\u3087\u308e\u3095-\u3096\u309b-\u309c\u30a1\u30a3\u30a5\u30a7\u30a9\u30c3\u30e3\u30e5\u30e7\u30ee\u30f5-\u30f6\u3127\u31f0-\u31ff\u32ff-\u3357\u337b-\u337f\ufe50-\ufe52\uff01\uff0c\uff0e\uff1f\U0001f200-\U0001f201",
    "u": "\xa7\xa9\xae\xb1\xbc-\xbe\xd7\xf7\u02ea-\u02eb\u1100-\u11ff\u1401-\u167f\u18b0-\u18ff\u2016\u2020-\u2021\u2030-\u2031\u203b-\u203c\u2042\u2047-\u2049\u2051\u2065\u20dd-\u20e0\u20e2-\u20e4\u2100-\u2101\u2103-\u2109\u210f\u2113-\u2114\u2116-\u2117\u211e-\u2123\u2125\u2127\u2129\u212e\u2135-\u213f\u2145-\u214a\u214c-\u214d\u214f-\u2189\u218c-\u218f\u221e\u2234-\u2235\u2300-\u2307\u230c-\u231f\u2324-\u2328\u232b\u237d-\u239a\u23be-\u23cd\u23cf\u23d1-\u23db\u23e2-\u2422\u2424-\u24ff\u25a0-\u2619\u2620-\u2767\u2776-\u2793\u2b12-\u2b2f\u2b50-\u2b59\u2b97\u2bb8-\u2bd1\u2bd3-\u2beb\u2bf0-\u2bff\u2e50-\u2e51\u2e80-\u3000\u3003-\u3007\u3012-\u3013\u3020-\u302f\u3031-\u3040\u3042\u3044\u3046\u3048\u304a-\u3062\u3064-\u3082\u3084\u3086\u3088-\u308d\u308f-\u3094\u3097-\u309a\u309d-\u309f\u30a2\u30a4\u30a6\u30a8\u30aa-\u30c2\u30c4-\u30e2\u30e4\u30e6\u30e8-\u30ed\u30ef-\u30f4\u30f7-\u30fb\u30fd-\u3126\u3128-\u31ef\u3200-\u32fe\u3358-\u337a\u3380-\ua4cf\ua960-\ua97f\uac00-\ud7ff\ue000-\ufaff\ufe10-\ufe1f\ufe30-\ufe48\ufe53-\ufe57\ufe5f-\ufe62\ufe67-\ufe6f\uff02-\uff07\uff0a-\uff0b\uff0f-\uff19\uff20-\uff3a\uff3c\uff3e\uff40-\uff5a\uffe0-\uffe2\uffe4-\uffe7\ufff0-\ufff8\ufffc-\ufffd\U00010980-\U0001099f\U00011580-\U000115ff\U00011a00-\U00011abf\U00013000-\U0001345f\U00014400-\U0001467f\U00016fe0-\U00018d7f\U0001aff0-\U0001b2ff\U0001cf00-\U0001cfcf\U0001d000-\U0001d1ff\U0001d2e0-\U0001d37f\U0001d800-\U0001daaf\U0001f000-\U0001f1ff\U0001f202-\U0001f7ff\U0001f900-\U0001faff\U00020000-\U0002fffd\U00030000-\U0003fffd\U000f0000-\U000ffffd\U00100000-\U0010fffd"
}
ascii_vertical_orientation: dict[str, str] = {
    "^r": "",
    "^tr": "\x00-\U0010ffff",
    "^tu": "\x00-\U0010ffff",
    "^u": "\x00-\U0010ffff",
    "r": "\x00-\U0010ffff",
    "tr": "",
    "tu": "",
    "u": ""
}
