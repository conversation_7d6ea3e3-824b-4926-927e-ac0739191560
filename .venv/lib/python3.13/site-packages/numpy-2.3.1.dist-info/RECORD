../../../bin/f2py,sha256=9iVro1oYWz4swZBn9IgOBGAn3hMgTi2qPulra80d3HE,306
../../../bin/numpy-config,sha256=ciUGamtO5bhQjp50ftMq6kv_E9vyyZWURg0JIAWv2lo,306
numpy-2.3.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
numpy-2.3.1.dist-info/LICENSE.txt,sha256=2EcVAAxrP9hxmyAnI4tvZFfLhTsutwI4IaWFlJOH978,47800
numpy-2.3.1.dist-info/METADATA,sha256=YB9T_NRy5NFfcg0ypNosRv7WJd-JJQDKVXl2wbf3r7s,62098
numpy-2.3.1.dist-info/RECORD,,
numpy-2.3.1.dist-info/WHEEL,sha256=pfpJ73u9f5n4jdRUwLfi5e7HknOIECF6VOOE9OTuurY,122
numpy-2.3.1.dist-info/entry_points.txt,sha256=7Cb63gyL2sIRpsHdADpl6xaIW5JTlUI-k_yqEVr0BSw,220
numpy/__config__.py,sha256=2zi05tVckqhPiDprbKOvodi4GfOdBSt0kcDTg5czDVY,4867
numpy/__config__.pyi,sha256=7nE-kUNs2lWPIpofTastbf2PCMgCka7FCiK5jrFkDYE,2367
numpy/__init__.cython-30.pxd,sha256=qT7d9_TWkj4UsfpY1uaBUmcYflptcjZfDGZsYJth8rU,47123
numpy/__init__.pxd,sha256=BFYYkcQUcrl0Ee8ReoQiA0wgtxsWeIGovC8jYeEw5qg,43758
numpy/__init__.py,sha256=gjanU4Bds0wp75zQNpTgr4g7YyXrT9JGzIPSvguEfok,25226
numpy/__init__.pyi,sha256=O7OKQ4eHechvTz_TjS4YOb2-c71WyS8gYnI4VgYdPIg,213225
numpy/__pycache__/__config__.cpython-313.pyc,,
numpy/__pycache__/__init__.cpython-313.pyc,,
numpy/__pycache__/_array_api_info.cpython-313.pyc,,
numpy/__pycache__/_configtool.cpython-313.pyc,,
numpy/__pycache__/_distributor_init.cpython-313.pyc,,
numpy/__pycache__/_expired_attrs_2_0.cpython-313.pyc,,
numpy/__pycache__/_globals.cpython-313.pyc,,
numpy/__pycache__/_pytesttester.cpython-313.pyc,,
numpy/__pycache__/conftest.cpython-313.pyc,,
numpy/__pycache__/dtypes.cpython-313.pyc,,
numpy/__pycache__/exceptions.cpython-313.pyc,,
numpy/__pycache__/matlib.cpython-313.pyc,,
numpy/__pycache__/version.cpython-313.pyc,,
numpy/_array_api_info.py,sha256=NzJSuf8vutjGSqiqahq3jRI3SxMX4X1cva4J6dFv4EU,10354
numpy/_array_api_info.pyi,sha256=QP_tYDbjtTOPtJECk3ehRXOQ24QM8TZjAfWX8XAsZCM,4864
numpy/_configtool.py,sha256=EFRJ3pazTxYhE9op-ocWyKTLZrrpFhfmmS_tWrq8Cxo,1007
numpy/_configtool.pyi,sha256=d4f22QGwpb1ZtDk-1Sn72ftvo4incC5E2JAikmjzfJI,24
numpy/_core/__init__.py,sha256=yJ0iy1fXk9ogCFnflCWzBBLwlKSS-xlQWCpWCozaT6c,5542
numpy/_core/__init__.pyi,sha256=Mj2I4BtqBVNUZVs5o1T58Z7wSaWjfhX0nCl-a0ULjgA,86
numpy/_core/__pycache__/__init__.cpython-313.pyc,,
numpy/_core/__pycache__/_add_newdocs.cpython-313.pyc,,
numpy/_core/__pycache__/_add_newdocs_scalars.cpython-313.pyc,,
numpy/_core/__pycache__/_asarray.cpython-313.pyc,,
numpy/_core/__pycache__/_dtype.cpython-313.pyc,,
numpy/_core/__pycache__/_dtype_ctypes.cpython-313.pyc,,
numpy/_core/__pycache__/_exceptions.cpython-313.pyc,,
numpy/_core/__pycache__/_internal.cpython-313.pyc,,
numpy/_core/__pycache__/_machar.cpython-313.pyc,,
numpy/_core/__pycache__/_methods.cpython-313.pyc,,
numpy/_core/__pycache__/_string_helpers.cpython-313.pyc,,
numpy/_core/__pycache__/_type_aliases.cpython-313.pyc,,
numpy/_core/__pycache__/_ufunc_config.cpython-313.pyc,,
numpy/_core/__pycache__/arrayprint.cpython-313.pyc,,
numpy/_core/__pycache__/cversions.cpython-313.pyc,,
numpy/_core/__pycache__/defchararray.cpython-313.pyc,,
numpy/_core/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/_core/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/_core/__pycache__/function_base.cpython-313.pyc,,
numpy/_core/__pycache__/getlimits.cpython-313.pyc,,
numpy/_core/__pycache__/memmap.cpython-313.pyc,,
numpy/_core/__pycache__/multiarray.cpython-313.pyc,,
numpy/_core/__pycache__/numeric.cpython-313.pyc,,
numpy/_core/__pycache__/numerictypes.cpython-313.pyc,,
numpy/_core/__pycache__/overrides.cpython-313.pyc,,
numpy/_core/__pycache__/printoptions.cpython-313.pyc,,
numpy/_core/__pycache__/records.cpython-313.pyc,,
numpy/_core/__pycache__/shape_base.cpython-313.pyc,,
numpy/_core/__pycache__/strings.cpython-313.pyc,,
numpy/_core/__pycache__/umath.cpython-313.pyc,,
numpy/_core/_add_newdocs.py,sha256=ySKuP_4sVPNLHp1ojgTMhSRWi3d18CcBFHFHkD8Xf-U,208893
numpy/_core/_add_newdocs.pyi,sha256=r__d_-GHkfjzuZ0qyjDztsKgdc1eIyeN-cBoYVgMBuo,168
numpy/_core/_add_newdocs_scalars.py,sha256=Z5WcIAXy2Vs8kWLCzgyvxWVH0CAl-O64YFK3ttbU7yc,12600
numpy/_core/_add_newdocs_scalars.pyi,sha256=ZnIk0TgL0szrv6SPCH-4dF469Q_92UvV5_ek47Oj7HM,573
numpy/_core/_asarray.py,sha256=fCNHLaaCP-5Ia-RR_bIrHxWY3xklcmvlZiGhJIDiKLM,3911
numpy/_core/_asarray.pyi,sha256=QHyb8DM_9U0otRugoNIyKjtvTVS3dZLn6DSxGi_ZU4U,1073
numpy/_core/_dtype.py,sha256=cM6JnjoHLURWCHgN8VmQyjeiiDjcwhB5L_fPMOe1uuM,10547
numpy/_core/_dtype.pyi,sha256=turm6RyVVEGKm6antqWWnyA0bnS2AuMwmKeFj-9mYHA,1851
numpy/_core/_dtype_ctypes.py,sha256=KPPlakDsPkuThSOr5qFwW0jJ9VnjbvW4EWhObCHYGIE,3726
numpy/_core/_dtype_ctypes.pyi,sha256=VwEZFViCPuHlCURv2jpJp9sbHh2hYUpzC_FRZNNGMMw,3682
numpy/_core/_exceptions.py,sha256=X8Eg1hq1uU8L9wiOwFo2jRq6S0vnjCdgYFHj3hAW9Co,5159
numpy/_core/_exceptions.pyi,sha256=ESXpijoEK0HrPy0dQYtjO62-Krd0419WLlrDROqwTyU,1900
numpy/_core/_internal.py,sha256=YZ6nMGVOvfTD1nzk2XqRdz8k05WVnYGiljb1TnHvMq8,28981
numpy/_core/_internal.pyi,sha256=2V2rXMQocZZHw8z_9HSrUi3LNGxaxA1nm0B0fcofjU8,2654
numpy/_core/_machar.py,sha256=YUX24XYbxXJ79KrWar27FlDYKfeodr_RCkE7w0bETqs,11569
numpy/_core/_machar.pyi,sha256=ESXpijoEK0HrPy0dQYtjO62-Krd0419WLlrDROqwTyU,1900
numpy/_core/_methods.py,sha256=4qiUUES5wnOFeXnPavtqqMVhZ09ZZeSKlwqdPw2eKSI,9430
numpy/_core/_methods.pyi,sha256=5HzEt2Z0-vxQfS1QJKDlTvNyLXcinNsja-xQiehMGbw,526
numpy/_core/_multiarray_tests.cpython-313-darwin.so,sha256=JQtG9IkGMebqed2_IUnI9d1z9_nCdGGxtkjBLqzO80A,120232
numpy/_core/_multiarray_umath.cpython-313-darwin.so,sha256=sHlEd7fQ6vMbCIyc-g-hICBNareC3dSo4iWSB5c6WYk,3450040
numpy/_core/_operand_flag_tests.cpython-313-darwin.so,sha256=Y99t3Iasmbyg6Vys_YcwQfeh_SEH-WrpPux-k2-KTuw,51240
numpy/_core/_rational_tests.cpython-313-darwin.so,sha256=Qy1XJOus8VAmZyFJBgkktX4asrZ2FSPX0iOgVl9bRYw,72760
numpy/_core/_simd.cpython-313-darwin.so,sha256=uy6bbJaG_g2S_RMrm09cYHXrBLF9jSNHxhHh9lsZykk,339592
numpy/_core/_simd.pyi,sha256=2z2sFPgXr3KRzHltbt31HVrhkXM0VwXFp1lUjxaRMAM,669
numpy/_core/_string_helpers.py,sha256=6Smgoi6oD2CunjwBSr9BZ20HkCnvW6nTPblTOU3pWng,2845
numpy/_core/_string_helpers.pyi,sha256=xLlLKJHutEYzyKnTG2k7clcWvVUTvD319SjnKmDXuac,358
numpy/_core/_struct_ufunc_tests.cpython-313-darwin.so,sha256=Wr3lxt-SdTTndoDT5cyaIg7sH8ClN4UJEGj63bywhZc,51464
numpy/_core/_type_aliases.py,sha256=msFHBkZ2s1wKQyuguK_cF6NBS0_3AOww7j3oh26mo3Q,3489
numpy/_core/_type_aliases.pyi,sha256=Tn1Ex4bAGQa1HuMx0Vn-tEBl3HDF_uesTzmiSrz81kQ,2388
numpy/_core/_ufunc_config.py,sha256=hVIyOmLjFYdZQY5plKWuOMk-U7UzeYSEo4ygiXOFcBU,15052
numpy/_core/_ufunc_config.pyi,sha256=rh1jhYnkafjGvrc3ytC5mOSwRnjwhoggw8yDeLCS3jc,972
numpy/_core/_umath_tests.cpython-313-darwin.so,sha256=s72MK_crV1fdHW92zhnEf88hXjqMmEMzDGOha3jSjBE,71680
numpy/_core/arrayprint.py,sha256=AAAvkrI0U6Pa_wZOnpuVZBpdsCCjpYpcWF8sA_SPYbg,65278
numpy/_core/arrayprint.pyi,sha256=ogMYnp2ipEfagADzRaRK9ySGAfH_oabGNJegiA6LicY,6971
numpy/_core/cversions.py,sha256=H_iNIpx9-hY1cQNxqjT2d_5SXZhJbMo_caq4_q6LB7I,347
numpy/_core/defchararray.py,sha256=1tSvLWEeac20DodpDBxapJKwwczpJG1lVy2qjScIVXg,38007
numpy/_core/defchararray.pyi,sha256=lBHpxupg3waq0n17ohMgn0liYZ4-CJdhlsxQhuZqm2I,26872
numpy/_core/einsumfunc.py,sha256=heFeCiEKji-qfVk8zAZ1b5bKm-MUMLzCETMQ7yyHBhc,52820
numpy/_core/einsumfunc.pyi,sha256=b10CKdAeLEryabwRMdiW1cKdNyqWLa5kMV7O2_X8g3A,4893
numpy/_core/fromnumeric.py,sha256=s0f6WfkIRVwFZMlDrdYb3EjyF9vMGr0bms0Pc-VcOAM,143882
numpy/_core/fromnumeric.pyi,sha256=VoUF-d31OuZYaRIi-duoYAABOADe4KjbBhFFx3Hd_Mc,42034
numpy/_core/function_base.py,sha256=QT1pbll_8rf_3ZsGtLQoAeQ1OSqCqeAGtMTzPAE1I_w,19683
numpy/_core/function_base.pyi,sha256=A9BlWQeiX08iIwDQJ6W1FUhy2qrRPVenXtHiEnPkt0k,7064
numpy/_core/getlimits.py,sha256=32Qe7tlBFdyiDvdSjG1cp2a0NJ0rSMxeDRij3agiPrg,26101
numpy/_core/getlimits.pyi,sha256=q30hQ3wDenmxoZUSoSOqyVrZZVGlsixXCHe6QUthbp8,61
numpy/_core/include/numpy/__multiarray_api.c,sha256=ndBF5wbdd7F8_zWvR52MDO0Qm15_PrCCBlSk4dky4F8,12698
numpy/_core/include/numpy/__multiarray_api.h,sha256=6ep4M4s0Cxoj4DgJGns-0___TdSqDJoUPnZr0BBYwkU,61639
numpy/_core/include/numpy/__ufunc_api.c,sha256=Fg7WlH4Ow6jETKRArVL_QF11ABKYz1VpOve56_U3E0w,1755
numpy/_core/include/numpy/__ufunc_api.h,sha256=J5h9KHdntM27XQdq1PwHwI7V2v-sOx6AIbgCwP8mg9M,13175
numpy/_core/include/numpy/_neighborhood_iterator_imp.h,sha256=s-Hw_l5WRwKtYvsiIghF0bg-mA_CgWnzFFOYVFJ-q4k,1857
numpy/_core/include/numpy/_numpyconfig.h,sha256=9SmNX4EmQWc9ImrbFnMHvtVLtTMkbadu2KEcLyS2t4M,928
numpy/_core/include/numpy/_public_dtype_api_table.h,sha256=n6_Kb98SyvsR_X7stiNA6VuGp_c5W1e4fMVcJdO0wis,4574
numpy/_core/include/numpy/arrayobject.h,sha256=mU5vpcQ95PH1j3bp8KYhJOFHB-GxwRjSUsR7nxlTSRk,204
numpy/_core/include/numpy/arrayscalars.h,sha256=LlyrZIa_5td11BfqfMCv1hYbiG6__zxxGv1MRj8uIVo,4243
numpy/_core/include/numpy/dtype_api.h,sha256=Gn37RzObmcTsL6YUYY9aG22Ct8F-r4ZaC53NPFqaIso,19238
numpy/_core/include/numpy/halffloat.h,sha256=TRZfXgipa-dFppX2uNgkrjrPli-1BfJtadWjAembJ4s,1959
numpy/_core/include/numpy/ndarrayobject.h,sha256=MnykWmchyS05ler_ZyhFIr_0j6c0IcndEi3X3n0ZWDk,12057
numpy/_core/include/numpy/ndarraytypes.h,sha256=kS9uirBf_ewXdIgsmRQETk3aQXeSPjLPCa6hlX5By-0,65810
numpy/_core/include/numpy/npy_2_compat.h,sha256=wdjB7_-AtW3op67Xbj3EVH6apSF7cRG6h3c5hBz-YMs,8546
numpy/_core/include/numpy/npy_2_complexcompat.h,sha256=eE9dV_Iq3jEfGGJFH_pQjJnvC6eQ12WgOB7cZMmHByE,857
numpy/_core/include/numpy/npy_3kcompat.h,sha256=grN6W1n7benj3F2pSAOpl_s6vn1Y50QfAP-DaleD7cA,9648
numpy/_core/include/numpy/npy_common.h,sha256=-05bavbk44KUjy5Q-qnM5YzU32VJRv0N8ozfCI_SKcE,32586
numpy/_core/include/numpy/npy_cpu.h,sha256=Vw8mVPm1fGmLdeLV3RoBZnBMMXA8cghgwRdWhlkDLi4,4225
numpy/_core/include/numpy/npy_endian.h,sha256=vvK7ZlOt0vgqTVrIyviWzoxQz70S-BvflS4Z_k6X5XE,2834
numpy/_core/include/numpy/npy_math.h,sha256=aeSFs60QbWPy1gIPyHDPrYExifm5mbDAcjP_mLk_PF0,18858
numpy/_core/include/numpy/npy_no_deprecated_api.h,sha256=0yZrJcQEJ6MCHJInQk5TP9_qZ4t7EfBuoLOJ34IlJd4,678
numpy/_core/include/numpy/npy_os.h,sha256=hlQsg_7-RkvS3s8OM8KXy99xxyJbCm-W1AYVcdnO1cw,1256
numpy/_core/include/numpy/numpyconfig.h,sha256=FGuDPIr0gTFYgUzhVMXqq5BIQL-WqgmXfp003cUwpWE,7333
numpy/_core/include/numpy/random/LICENSE.txt,sha256=-8U59H0M-DvGE3gID7hz1cFGMBJsrL_nVANcOSbapew,1018
numpy/_core/include/numpy/random/bitgen.h,sha256=49AwKOR552r-NkhuSOF1usb_URiMSRMvD22JF5pKIng,488
numpy/_core/include/numpy/random/distributions.h,sha256=W5tOyETd0m1W0GdaZ5dJP8fKlBtsTpG23V2Zlmrlqpg,9861
numpy/_core/include/numpy/random/libdivide.h,sha256=ew9MNhPQd1LsCZiWiFmj9IZ7yOnA3HKOXffDeR9X1jw,80138
numpy/_core/include/numpy/ufuncobject.h,sha256=BengvqXqiy4ipzz23KQi1Kldy9ybYUs4Sp5yA73VgiU,11780
numpy/_core/include/numpy/utils.h,sha256=wMNomSH3Dfj0q78PrjLVtFtN-FPo7UJ4o0ifCUO-6Es,1185
numpy/_core/lib/libnpymath.a,sha256=JZyMdASIo5yu99Z-PhbUbEl6r_0TUr5Z8jHMWlNzDZ0,35456
numpy/_core/lib/npy-pkg-config/mlib.ini,sha256=_LsWV1eStNqwhdiYPa2538GL46dnfVwT4MrI1zbsoFw,147
numpy/_core/lib/npy-pkg-config/npymath.ini,sha256=0iMzarBfkkZ_EXO95_kz-SHZRcNIEwIeOjE_esVBkRQ,361
numpy/_core/lib/pkgconfig/numpy.pc,sha256=5Myb9RBRiSk8r56ceJTWyry4UZHpRCJ9tYCFvmP7UzQ,191
numpy/_core/memmap.py,sha256=yIsQ6n9kpZulggRJJFkTbjVwnB4leoyizvUpc2iU4n8,12651
numpy/_core/memmap.pyi,sha256=_LKjb_PuhcQwpqc2lFaL379DYzQ9PtuKdlVV3jXOYEM,47
numpy/_core/multiarray.py,sha256=zwHBdyOoxiBRcOhG2QB_xBAYm-p8ARSpQbye9EzrrBo,58155
numpy/_core/multiarray.pyi,sha256=Uy5Unmczfk7Pyz8Ohgh_5g4ASY7aZ0ZYpmhhmPnG6OA,32150
numpy/_core/numeric.py,sha256=_DcnvXu6oaHXSi9Q-BV9yGzfx7tc9iCx69r9MnJDm5g,82322
numpy/_core/numeric.pyi,sha256=ZSWTBi2kdP7BPG3KMGJWJIlqM9BLKFmgq_xgK_GnDUo,19042
numpy/_core/numerictypes.py,sha256=mKPbsOzX9vyWQEv4jlf4xnlPfP4IYAXeILHFdb2FS0I,15957
numpy/_core/numerictypes.pyi,sha256=Kp4_fEg_Wj_Yv8xvI7H1TJXrDVsxb96oIH5EmnQyW1c,3270
numpy/_core/overrides.py,sha256=MtgzOBavG7wzQYCA7O7ArdCJVV72STIb_cvkWBuDLJE,7241
numpy/_core/overrides.pyi,sha256=2lHte4EbOTDQvknjVfO71RgiLXnOpGQky5j2meS09JU,1713
numpy/_core/printoptions.py,sha256=NFpvy5bnjbvqnKeqQt0veEExpAAYAVNoiGXH3pglWAc,1056
numpy/_core/printoptions.pyi,sha256=eNiliCnDuZBxla6X9kwZ-7YiCn-UtMbT-U_qTnw8l9w,594
numpy/_core/records.py,sha256=hoXCDswM6hbytiGdYGkhRISzQjnqImXcIdGlNuOUDX4,36767
numpy/_core/records.pyi,sha256=tob9AxABbCXsO--gWXX-pD5Bo50NgCXKOt4JstVESjY,8935
numpy/_core/shape_base.py,sha256=7yDPrIXTmmBnZMUStHXsq1iJNiGmIxEAcepxQ9o-JVQ,32738
numpy/_core/shape_base.pyi,sha256=Qgfi1izbvKgRWAojCMXw3HsONgvsryFCsDhAvNI1dZE,4753
numpy/_core/strings.py,sha256=yjdeNG2e0wpljpnwGISi7NXVLD4ttCM5vAYSSV1yI8k,50642
numpy/_core/strings.pyi,sha256=Fyjq70ZP70BzV3Ov490dxX5EOv76sgnxA7qVBxeXuRU,13502
numpy/_core/tests/__pycache__/_locales.cpython-313.pyc,,
numpy/_core/tests/__pycache__/_natype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test__exceptions.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_abc.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_api.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_argparse.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_api_info.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_coercion.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_interface.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arraymethod.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arrayobject.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arrayprint.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_casting_floatingpoint_errors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_casting_unittests.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_conversion_utils.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cpu_dispatcher.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cpu_features.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_custom_dtypes.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cython.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_datetime.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_defchararray.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_dlpack.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_dtype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_einsum.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_errstate.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_extint128.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_function_base.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_getlimits.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_half.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_hashtable.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_indexerrors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_indexing.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_item_selection.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_limited_api.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_longdouble.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_machar.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_mem_overlap.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_mem_policy.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_memmap.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_multiarray.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_multithreading.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_nditer.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_nep50_promotions.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_numeric.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_numerictypes.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_overrides.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_print.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_protocols.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_records.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalar_ctors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalar_methods.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarbuffer.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarinherit.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarmath.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarprint.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_shape_base.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_simd.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_simd_module.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_stringdtype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_strings.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_ufunc.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath_accuracy.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath_complex.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_unicode.cpython-313.pyc,,
numpy/_core/tests/_locales.py,sha256=lvHqUJVMsrE7Jh3N_KpO5fGBZgID-l3Zr4-_RrH1ZNM,2176
numpy/_core/tests/_natype.py,sha256=YCAkuhvWuMjTjt-C0VjA8zzui-KoioNwOmAYnvf6KR0,6525
numpy/_core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/_core/tests/data/generate_umath_validation_data.cpp,sha256=BQakB5o8Mq60zex5ovVO0IatNa7xbF8JvXmtk6373So,5842
numpy/_core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/_core/tests/data/umath-validation-set-README.txt,sha256=pxWwOaGGahaRd-AlAidDfocLyrAiDp0whf5hC7hYwqM,967
numpy/_core/tests/data/umath-validation-set-arccos.csv,sha256=yBlz8r6RnnAYhdlobzGGo2FKY-DoSTQaP26y8138a3I,61365
numpy/_core/tests/data/umath-validation-set-arccosh.csv,sha256=0GXe7XG1Z3jXAcK-OlEot_Df3MetDQSlbm3MJ__iMQk,61365
numpy/_core/tests/data/umath-validation-set-arcsin.csv,sha256=w_Sv2NDn-mLZSAqb56JT2g4bqBzxYAihedWxHuf82uU,61339
numpy/_core/tests/data/umath-validation-set-arcsinh.csv,sha256=DZrMYoZZZyM1DDyXNUxSlzx6bOgajnRSLWAzxcPck8k,60289
numpy/_core/tests/data/umath-validation-set-arctan.csv,sha256=0aosXZ-9DYTop0lj4bfcBNwYVvjZdW13hbMRTRRTmV0,60305
numpy/_core/tests/data/umath-validation-set-arctanh.csv,sha256=HEK9ePx1OkKrXIKkMUV0IxrmsDqIlgKddiI-LvF2J20,61339
numpy/_core/tests/data/umath-validation-set-cbrt.csv,sha256=v855MTZih-fZp_GuEDst2qaIsxU4a7vlAbeIJy2xKpc,60846
numpy/_core/tests/data/umath-validation-set-cos.csv,sha256=0PNnDqKkokZ7ERVDgbes8KNZc-ISJrZUlVZc5LkW18E,59122
numpy/_core/tests/data/umath-validation-set-cosh.csv,sha256=JKC4nKr3wTzA_XNSiQvVUq9zkYy4djvtu2-j4ZZ_7Oc,60869
numpy/_core/tests/data/umath-validation-set-exp.csv,sha256=rUAWIbvyeKh9rPfp2n0Zq7AKq_nvHpgbgzLjAllhsek,17491
numpy/_core/tests/data/umath-validation-set-exp2.csv,sha256=djosT-3fTpiN_f_2WOumgMuuKgC_XhpVO-QsUFwI6uU,58624
numpy/_core/tests/data/umath-validation-set-expm1.csv,sha256=K7jL6N4KQGX71fj5hvYkzcMXk7MmQes8FwrNfyrPpgU,60299
numpy/_core/tests/data/umath-validation-set-log.csv,sha256=ynzbVbKxFzxWFwxHnxX7Fpm-va09oI3oK1_lTe19g4w,11692
numpy/_core/tests/data/umath-validation-set-log10.csv,sha256=NOBD-rOWI_FPG4Vmbzu3JtX9UA838f2AaDFA-waiqGA,68922
numpy/_core/tests/data/umath-validation-set-log1p.csv,sha256=tdbYWPqWIz8BEbIyklynh_tpQJzo970Edd4ek6DsPb8,60303
numpy/_core/tests/data/umath-validation-set-log2.csv,sha256=39EUD0vFMbwyoXoOhgCmid6NeEAQU7Ff7QFjPsVObIE,68917
numpy/_core/tests/data/umath-validation-set-sin.csv,sha256=8PUjnQ_YfmxFb42XJrvpvmkeSpEOlEXSmNvIK4VgfAM,58611
numpy/_core/tests/data/umath-validation-set-sinh.csv,sha256=XOsBUuPcMjiO_pevMalpmd0iRv2gmnh9u7bV9ZLLg8I,60293
numpy/_core/tests/data/umath-validation-set-tan.csv,sha256=Hv2WUMIscfvQJ5Y5BipuHk4oE4VY6QKbQp_kNRdCqYQ,60299
numpy/_core/tests/data/umath-validation-set-tanh.csv,sha256=iolZF_MOyWRgYSa-SsD4df5mnyFK18zrICI740SWoTc,60299
numpy/_core/tests/examples/cython/__pycache__/setup.cpython-313.pyc,,
numpy/_core/tests/examples/cython/checks.pyx,sha256=nw6o0nlj3SfNQP3McS10zVH9UCZiITBdAi5yO4gm9Qo,10774
numpy/_core/tests/examples/cython/meson.build,sha256=uuXVPKemNVMQ5MiEDqS4BXhwGHa96JHjS50WxZuJS_8,1268
numpy/_core/tests/examples/cython/setup.py,sha256=JM6UnDql7LsAnRo6p9G-nRz3dfnoy9fHF6YVKy1OzdA,859
numpy/_core/tests/examples/limited_api/__pycache__/setup.cpython-313.pyc,,
numpy/_core/tests/examples/limited_api/limited_api1.c,sha256=htSR9ER3S8AJqv4EZMsrxQ-SufTIlXNpuFI6MXQs87w,346
numpy/_core/tests/examples/limited_api/limited_api2.pyx,sha256=1q4I59pdkCmMhLcYngN_XwQnPoLmDEo1uTGnhrLRjDc,203
numpy/_core/tests/examples/limited_api/limited_api_latest.c,sha256=ltBLbrl1g9XxD2wvN_-g3NhIizc8mxnh2Z6wCyXo-8E,452
numpy/_core/tests/examples/limited_api/meson.build,sha256=YM5RwW_waFymlWSHFhCCOHO6KCknooN0jCiqScL0i5M,1627
numpy/_core/tests/examples/limited_api/setup.py,sha256=Y6tgsOF58qe7eG2QmRQHG2wacZWfpbJLT8u-5OamjqA,437
numpy/_core/tests/test__exceptions.py,sha256=luMT6vPIdf6LuwFNGyT-xLMZaKZEYYOFzFpMaesojoE,2922
numpy/_core/tests/test_abc.py,sha256=9y2SsJdkPeV0oW6dsROPZOcQ72_mXie1uU2yPN93wzo,2221
numpy/_core/tests/test_api.py,sha256=NiqlxYyBOZlKVKIWs_vQTg6ZnOk5iE63nbz1GBdHXeI,22954
numpy/_core/tests/test_argparse.py,sha256=pfFfRr0grfOt-6Y7D8q9yPmz8Fcx4UbUxLpe96Tk9Xg,2870
numpy/_core/tests/test_array_api_info.py,sha256=PZ2EzS9pq4nLZRAvvUSOb2Ke5p7pb4u4P4HKLRZjstw,3063
numpy/_core/tests/test_array_coercion.py,sha256=PJ3s7psngDM084R2x7luAHVkHoa31TDiH1FiZpUWSfs,34897
numpy/_core/tests/test_array_interface.py,sha256=l39VuV4nCdIeV1RUvMtjjPohAgIvJP-V3GQ5MaPrVK8,7843
numpy/_core/tests/test_arraymethod.py,sha256=my4I9YjpVGLwN1GMbuoEhBZEJN0PuH6R2wtvGHcfoWI,3223
numpy/_core/tests/test_arrayobject.py,sha256=aVv2eGjunCMEDFgmFujxMpk4xb-zo1MQrFcwQLfblx0,2596
numpy/_core/tests/test_arrayprint.py,sha256=6UmL93wltbIDKdhF_WcdPRH5mztX0wyzuBy6PYW3R_o,50738
numpy/_core/tests/test_casting_floatingpoint_errors.py,sha256=cER1YCNEwq67uAPX0QhkJonb5oA4Ws1_t0Z2AWJjYJg,5076
numpy/_core/tests/test_casting_unittests.py,sha256=HH849h4ox1dejLB4aFX2B9tSGf0WhVvPZBPJT4yTOAA,34336
numpy/_core/tests/test_conversion_utils.py,sha256=HAIdSRUit1lhSQEn-UVPTwyNxKjP9bSr8NGeHXnp6ew,6362
numpy/_core/tests/test_cpu_dispatcher.py,sha256=26vob-nCPkjtxf9lRlQvwoTR92lqquyDGPgE5DIoii8,1570
numpy/_core/tests/test_cpu_features.py,sha256=lS9iIWWznKZgR8-G4ABZqznMTJGC343-FBaCG9ZHXmQ,15703
numpy/_core/tests/test_custom_dtypes.py,sha256=LZCbBeoyCcluhz_drg5neyiAsoTaK-6DjB4l3LaNnTw,11766
numpy/_core/tests/test_cython.py,sha256=hLdTcd5wbzMXOx_OyQEzNyFWm-rIcWto7LpCl1SNdIU,10186
numpy/_core/tests/test_datetime.py,sha256=fVk7HADvcuiFzs19MTF4sAvp96jCBWQ7GWeNBQZKBPs,121786
numpy/_core/tests/test_defchararray.py,sha256=hmMd5Wv5PjTEIuBXq_DopSqJsnp-qJ8ub5BBGRKIUEw,30629
numpy/_core/tests/test_deprecations.py,sha256=CayfNUVMMj4BYTIFdYR4xvL2Sy2CTLN7VTABe0HIlxg,17101
numpy/_core/tests/test_dlpack.py,sha256=Lfi3Xd2umxJ4W8fJht5epHlYWwTKx7MB47i7dcOIpq8,5830
numpy/_core/tests/test_dtype.py,sha256=e1ZLn0xj8FrlxK3FeHOOsoQ-xV17-FMM7mh7VpuuVhs,78797
numpy/_core/tests/test_einsum.py,sha256=Sixz-ZogKZmnFz3t49voD6AsCxmxUl_c_DHxT9rdscE,56277
numpy/_core/tests/test_errstate.py,sha256=czhSWJJ8mdDpkh76pAxU2-d4ebMyopyk2D_CC-2lzI0,4627
numpy/_core/tests/test_extint128.py,sha256=F6TAH3PlGON3CNz-B4hunClNUTQYQ2R8CkvaX2Zqeo4,5625
numpy/_core/tests/test_function_base.py,sha256=x6rHdbqXtHj07Oml_5DslnG6y8jm0XfW4RdV0Q_lHHA,17651
numpy/_core/tests/test_getlimits.py,sha256=CAHTLA8QIYVXTLWCGAISUZaAJ-xd_cBnSdYaOGuLWn8,6976
numpy/_core/tests/test_half.py,sha256=QSKuHAfa8NWvl0A51-XcV0UOIvk-ooLy6pndq90hr6k,24425
numpy/_core/tests/test_hashtable.py,sha256=m9-IRALLhU5liPuAk4v-ZQTVQ4s5XtLhL6xRXf5QTOE,1147
numpy/_core/tests/test_indexerrors.py,sha256=mU2MJbdpbrcvxLZqZR293So4ZJxMH4apAjqXufRyOis,4726
numpy/_core/tests/test_indexing.py,sha256=lU0jP4UvEe2_MUiAhy4_GD1zvpdIwUrHviu0MJhW_wQ,55421
numpy/_core/tests/test_item_selection.py,sha256=AoPUe3llYwKjv3dO1PW1qSml4SWrAAL3fNqpwKAku6w,6631
numpy/_core/tests/test_limited_api.py,sha256=75nz_t-jBdjKim6j-WW7WsD2rPnJ_KQ-zrRUiP3nVic,3463
numpy/_core/tests/test_longdouble.py,sha256=FjuntHkYe158dwWr7eYe_mlqkj7sQ9lQXKZ93CKF0Pc,12391
numpy/_core/tests/test_machar.py,sha256=Aw8icmrolAGmbIuXhUIYd4YvqIRR1I8GkcSx0J2c6yM,1067
numpy/_core/tests/test_mem_overlap.py,sha256=IGpRF2GnkLQxEiIizsVT0eWUtlgCcJQ4w0-BEjSpT_8,29219
numpy/_core/tests/test_mem_policy.py,sha256=pL6kBK8fgtRDTfMubFGGWnliTPWnS64uZ9l1H5qI8hk,16794
numpy/_core/tests/test_memmap.py,sha256=LtghbNqt9AOmAalIyZF3lepthcKircyNfb2-5_Tkj1c,8186
numpy/_core/tests/test_multiarray.py,sha256=qRs_A-uUTxPNMyorB0tespV5bZDmUP1V2SdYDf92UHA,399889
numpy/_core/tests/test_multithreading.py,sha256=VkvO2311ch8a_EeF7RTmhAQWvtHXuTZhqLVZZH1ovKI,8601
numpy/_core/tests/test_nditer.py,sha256=7y1wdYzpGdwEbHRc5xppx8FZ45cKxNrm3JKzUPvkhrE,136568
numpy/_core/tests/test_nep50_promotions.py,sha256=i6KpABBWFB5PWCdEv8kIjNQd7ryAPINS5m_Tnu7sDj4,10068
numpy/_core/tests/test_numeric.py,sha256=aM2TfTaSVE2fz0Z3nN72XoxSDvZzAdatwWpLYWGBBws,159748
numpy/_core/tests/test_numerictypes.py,sha256=r4ZvEN0E8efuqZhx2spCXA5Mr14mK1BRpmOZFRp0LhU,23271
numpy/_core/tests/test_overrides.py,sha256=0sDSmDWIr88GuCj0gOxdE3l0X_T5Hb5Wj2zfJDkOtvU,27518
numpy/_core/tests/test_print.py,sha256=_cuM-DIpljOkzErb2ggIgs9HvOYrtpRppaECF6xAo0c,6787
numpy/_core/tests/test_protocols.py,sha256=pbfumoRNnPhDP6PAPNIgLHUPPlmCdamCo4akkO8afjo,1173
numpy/_core/tests/test_records.py,sha256=PAMHzIPp2WWDm4JHFQ-cjPBWf4BDuQumIYo7UX-zElk,20547
numpy/_core/tests/test_regression.py,sha256=fJJnesLRUyPziCbYVM9LfLSS3qAMUz1-mzddhV9Br-U,95565
numpy/_core/tests/test_scalar_ctors.py,sha256=I3akKp6WdwsTGic8pYQC_c6AxPXPEXStywWOF0n_ivU,6724
numpy/_core/tests/test_scalar_methods.py,sha256=tx1RoZ03QsWblqg3Dv_JkaBFUOOILKZIqaEsFEs4tfE,9117
numpy/_core/tests/test_scalarbuffer.py,sha256=2mZblaScwhN8mdlQvUULAKt273B2ia-mjtNmL_2UxfQ,5638
numpy/_core/tests/test_scalarinherit.py,sha256=OIvSjrltdNSSP2c5HvDQ6pza3aKfmfgtixu1Zbahpcg,2587
numpy/_core/tests/test_scalarmath.py,sha256=gBHBZ5SQMru1A57FUEaIMk19GFdVLTRXiO9vVh4XVVc,46583
numpy/_core/tests/test_scalarprint.py,sha256=NS-FQDWICDcuDF5gxTQuG1Td1-EiOXIXufI-dwvKwxU,19705
numpy/_core/tests/test_shape_base.py,sha256=mRSruY7S84ula25ZoOvbcRg_ea_3C3338e1tmdmv1Uk,31536
numpy/_core/tests/test_simd.py,sha256=u8xSZ6HNLJ9-siYNIuyd0RA7FbD1BLEmnV5TGUrt1FU,48823
numpy/_core/tests/test_simd_module.py,sha256=JjXH4Yq-0K-R8FHqVDinNaqY_grb1fQFFyVTHGQ0pBg,3904
numpy/_core/tests/test_stringdtype.py,sha256=0xqwd0g8zulUqrU4ZVyN5RHzePw7YepUJKFbtcosUF8,56017
numpy/_core/tests/test_strings.py,sha256=9NBsxBBV5bRWo3oBS7FDtSR3J1-r-DpR7cv1zGrDs28,57834
numpy/_core/tests/test_ufunc.py,sha256=l6MOitgfeJn3TzZb6VuwGFoI4vI9UAw-wR34f1CG2i4,135051
numpy/_core/tests/test_umath.py,sha256=D7wSX7JvIk80znwd8GsxYZIzp62It75SBzvKOZHeOXE,193840
numpy/_core/tests/test_umath_accuracy.py,sha256=QCFAeiPN6rEO8fwDwJun4J1pCKm0bPsQK6-1pTYCMIY,5478
numpy/_core/tests/test_umath_complex.py,sha256=LZMd-divBHQQ7dS34obwvmStXa8aNez45VIVTwPg_jM,23627
numpy/_core/tests/test_unicode.py,sha256=qrQ7UC0yndXFYI7MiJu8y_I5jCK2lxOQcehE289MElk,12967
numpy/_core/umath.py,sha256=t_SQIHR7dkMF-VRp8dKyroOEd90oqNlzmgGwaH28qW8,2130
numpy/_core/umath.pyi,sha256=FIqmlQwQIueIrs-_QehV3guNEnJE2LxVs3NPCj38Vdo,2643
numpy/_distributor_init.py,sha256=FBSJdgVHlQca5BrQEVYPoFm6KSTJhIFnWtWbEkEhTSo,421
numpy/_distributor_init.pyi,sha256=6IvMzAmr0-Z6oqTkZcgXgrkJrQXVMjBih2AZvLdDgOE,27
numpy/_expired_attrs_2_0.py,sha256=zP31EXmbwygcOEzyetDEp-RxL9cUfbUUht956zaOSf8,3826
numpy/_expired_attrs_2_0.pyi,sha256=n2ipDUFTFS4puCD56dlNWGkVkw_b0M6cEyugo4Qh3HM,1253
numpy/_globals.py,sha256=k5ZVnzUbKNSLPmZ0URYwJN5C_7xIzfMNaaSsBSrPTuI,3091
numpy/_globals.pyi,sha256=IrHHIXmibXzgK0VUlECQLw4IEkveXSHo_ZWnTkfnLe4,280
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__pycache__/__init__.cpython-313.pyc,,
numpy/_pyinstaller/__pycache__/hook-numpy.cpython-313.pyc,,
numpy/_pyinstaller/hook-numpy.py,sha256=MU22pQ4AkUYPQWu5C8pRDpnYXElLJ8R0FGNYJUQpiVE,1362
numpy/_pyinstaller/hook-numpy.pyi,sha256=tAvtMPovoi-sur0D1NAo3_evSmYKLTh0bgRSC7QrCIk,349
numpy/_pyinstaller/tests/__init__.py,sha256=pdPbCTRwpCJamlyvIi9HZTlqAvK5HPbGu3oMA0cu2Rs,329
numpy/_pyinstaller/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/_pyinstaller/tests/__pycache__/pyinstaller-smoke.cpython-313.pyc,,
numpy/_pyinstaller/tests/__pycache__/test_pyinstaller.cpython-313.pyc,,
numpy/_pyinstaller/tests/pyinstaller-smoke.py,sha256=6iL-eHMQaG3rxnS5EgcvrCqElm9aKL07Cjr1FZJSXls,1143
numpy/_pyinstaller/tests/test_pyinstaller.py,sha256=8K-7QxmfoXCG0NwR0bhIgCNrDjGlrTzWnrR1sR8btgU,1135
numpy/_pytesttester.py,sha256=DjlYL8uINN2XWa3nnlX6gPGuoLjcx1Bie_PQzbp2cpA,6328
numpy/_pytesttester.pyi,sha256=VXCuwPYTb9-PF6nxXwibwBbre0hW9jIB4nkzmtm2kls,497
numpy/_typing/__init__.py,sha256=MG5Wv9dc3ZyOmDfidH5cFtykeyNM77ArC4R3UW7Tn-Y,7188
numpy/_typing/__pycache__/__init__.cpython-313.pyc,,
numpy/_typing/__pycache__/_add_docstring.cpython-313.pyc,,
numpy/_typing/__pycache__/_array_like.cpython-313.pyc,,
numpy/_typing/__pycache__/_char_codes.cpython-313.pyc,,
numpy/_typing/__pycache__/_dtype_like.cpython-313.pyc,,
numpy/_typing/__pycache__/_extended_precision.cpython-313.pyc,,
numpy/_typing/__pycache__/_nbit.cpython-313.pyc,,
numpy/_typing/__pycache__/_nbit_base.cpython-313.pyc,,
numpy/_typing/__pycache__/_nested_sequence.cpython-313.pyc,,
numpy/_typing/__pycache__/_scalars.cpython-313.pyc,,
numpy/_typing/__pycache__/_shape.cpython-313.pyc,,
numpy/_typing/__pycache__/_ufunc.cpython-313.pyc,,
numpy/_typing/_add_docstring.py,sha256=_3g7D-6HAQ3MT4X6DE07yLua9LqWFhskNVx1TS7X9O4,3999
numpy/_typing/_array_like.py,sha256=EPZUfJSjamvsWJ6Rs5ZwwA_5FhBpYdoifcVVtVcWPn0,4188
numpy/_typing/_callable.pyi,sha256=Zq3vN0V7VMwFRjyXl2ITcc8DdWKAB0fSlBQ52wmZrMI,11767
numpy/_typing/_char_codes.py,sha256=j07npk82Nb7Ira2z7ZTlU3UcOPwt2gM7qZKrPLdjT48,8764
numpy/_typing/_dtype_like.py,sha256=8M5RekLqdheEjWMIn4RnbkEzsS7jCatCiT0D5hg-53c,3762
numpy/_typing/_extended_precision.py,sha256=pknUqgak0FBNM-sERPqW-pFGH71_K-iehFSee5oQiqE,434
numpy/_typing/_nbit.py,sha256=KSbKwOKttob-5ytT5vCVkHrDMn0YHvyptTTyj_6AYcw,632
numpy/_typing/_nbit_base.py,sha256=nPZpsQltuR5B0iaAYF9qD2he_kXnmssv_RhaUNFsW-s,3058
numpy/_typing/_nbit_base.pyi,sha256=kHAqTmpYUWbQyTUVRs4NKKcDwiEJgUzWvvT1FQgQ89I,740
numpy/_typing/_nested_sequence.py,sha256=so1agYGHd5gDo_IBvvHqBB5lsqGbHqN_imyC5UHU-HI,2505
numpy/_typing/_scalars.py,sha256=LhXY2BTHmeYKzeIZfpjvuMn-5eOLjU2n9z7z1l5bKf8,944
numpy/_typing/_shape.py,sha256=6cFv-LbSyG9mlfSBOGGyul9Q_GUrlcHQC9JZa-m20cA,275
numpy/_typing/_ufunc.py,sha256=HOkaE-6wV0fd3rmHZGC39YAHIIf8tyvlzekD4y4GQxA,156
numpy/_typing/_ufunc.pyi,sha256=1Ni26dsi2fbH2oNvXDNNXaBPQQzdhkwA7VQ8eyuJS_c,26575
numpy/_utils/__init__.py,sha256=hVnZ7C0MCSNbMw-Zyq-MKCYStaGX6RzqFMnnh7ed4dE,3477
numpy/_utils/__init__.pyi,sha256=VxEygNvp90alV8zYsUSuDYNdF7BEucXUx3w55Ef7YXI,726
numpy/_utils/__pycache__/__init__.cpython-313.pyc,,
numpy/_utils/__pycache__/_convertions.cpython-313.pyc,,
numpy/_utils/__pycache__/_inspect.cpython-313.pyc,,
numpy/_utils/__pycache__/_pep440.cpython-313.pyc,,
numpy/_utils/_convertions.py,sha256=0xMxdeLOziDmHsRM_8luEh4S-kQdMoMg6GxNDDas69k,329
numpy/_utils/_convertions.pyi,sha256=4l-0UmPCyVA70UJ8WAd2A45HrKFSzgC0sFDBSnKcYiQ,118
numpy/_utils/_inspect.py,sha256=zFuJABH08D1Kgq_eecYkD1Ogg0OXp1t4oqjZxM0kdLk,7436
numpy/_utils/_inspect.pyi,sha256=wFajmQpCTXpMbJBbdiiyJMb29HkaMW0jEWLMqbQcQ5k,2255
numpy/_utils/_pep440.py,sha256=it9P4_oHXWw3BxdoVz7JPMuj5kxF5M7_BJ8Z1m9nu0w,13988
numpy/_utils/_pep440.pyi,sha256=xzYJoZ6DnjvgaKr8OsBwim77fAJ0xeQJI9XAt75gvfI,3870
numpy/char/__init__.py,sha256=xs6pprMdmNeXVfuTRkU3nF9qdhutWdPu5oaep2AjWmc,93
numpy/char/__init__.pyi,sha256=siwqDh7X7u4e0HGx3xg8eDaJVqy0_nac5y8UMzz-BcM,1540
numpy/char/__pycache__/__init__.cpython-313.pyc,,
numpy/conftest.py,sha256=pXdv-CKocoIEpr0DsYstu7TgqvNdzSvfiDNMlMwmqYk,8577
numpy/core/__init__.py,sha256=wJNaRF1UFOnZKqiBrsshWLjTGiEZ9rvWlcit0xj7Y0w,1290
numpy/core/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/__pycache__/__init__.cpython-313.pyc,,
numpy/core/__pycache__/_dtype.cpython-313.pyc,,
numpy/core/__pycache__/_dtype_ctypes.cpython-313.pyc,,
numpy/core/__pycache__/_internal.cpython-313.pyc,,
numpy/core/__pycache__/_multiarray_umath.cpython-313.pyc,,
numpy/core/__pycache__/_utils.cpython-313.pyc,,
numpy/core/__pycache__/arrayprint.cpython-313.pyc,,
numpy/core/__pycache__/defchararray.cpython-313.pyc,,
numpy/core/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/core/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/core/__pycache__/function_base.cpython-313.pyc,,
numpy/core/__pycache__/getlimits.cpython-313.pyc,,
numpy/core/__pycache__/multiarray.cpython-313.pyc,,
numpy/core/__pycache__/numeric.cpython-313.pyc,,
numpy/core/__pycache__/numerictypes.cpython-313.pyc,,
numpy/core/__pycache__/overrides.cpython-313.pyc,,
numpy/core/__pycache__/records.cpython-313.pyc,,
numpy/core/__pycache__/shape_base.cpython-313.pyc,,
numpy/core/__pycache__/umath.cpython-313.pyc,,
numpy/core/_dtype.py,sha256=GHBhfVtsVrP7v13IujEz9aGIENkYIdbfuRu-New1UnU,323
numpy/core/_dtype.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_dtype_ctypes.py,sha256=wX4m37b0zQgxlzT5OjE_uj2E5CpiX9E7HLFpO6h_lDY,351
numpy/core/_dtype_ctypes.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_internal.py,sha256=qxpHJELXNUcYJkJt1LktQuZm4BwYu4bXnMuBEOp6POU,949
numpy/core/_multiarray_umath.py,sha256=T88HZgFD5VCuXRCSeLbPoj99nKUSdgyw8xWyf6eqhxQ,2098
numpy/core/_utils.py,sha256=5fk18JN43Rg6YHvan6QjdrOeOuLtRlLVmP6MadBEJVA,923
numpy/core/arrayprint.py,sha256=Lbe4smWXYFzd9sO9LLJ5PZS4C3bSvLt6HRtwSE56xN8,339
numpy/core/defchararray.py,sha256=a9luvvni8gRrGVdKO7U_xwsFFvkzlxnVgxL75jLRmCI,347
numpy/core/einsumfunc.py,sha256=CNucINgUIrpiLQn4xPI_mogwjfKlFA3h7gwAvRVwb5M,339
numpy/core/fromnumeric.py,sha256=5TaonJVuC110qv3f3cqTtmjayTX0BmqJAgoAJn5H3ZI,343
numpy/core/function_base.py,sha256=vhjhzsEzDd11RHg6pilfMJO3X6k94an5RAJqj-nlzms,351
numpy/core/getlimits.py,sha256=6nCk4Tw0LjW7joWsprI5LiMzje1gsOjO2lSQ_OwBB8I,335
numpy/core/multiarray.py,sha256=bjdPLbvJuj61M6TZkbB5NXOCNmH4QbUq6g3ePkKP6TA,793
numpy/core/numeric.py,sha256=Ctk_QikyB2mM0xI0lBeB8YTUfTwQSXfVdpIMRtunbMo,360
numpy/core/numerictypes.py,sha256=bXwTwzUahzbHrFGhS5RkJOvb6TYEsQnQC5ww9mN-1Vw,347
numpy/core/overrides.py,sha256=1FZyb0U6JJuyojtxFvQ7HSJ2rpfhWec0F-X0mapCjc8,335
numpy/core/overrides.pyi,sha256=-3xfjHfa4UaCuhTVwwRN4EOM5uz9vZR0gMeTVvEdbYI,525
numpy/core/records.py,sha256=9yfFDxyOc68lXqfbaosgRNlw1dbWP8CRHzIPEtEtSgc,327
numpy/core/shape_base.py,sha256=2srdQtF1d8LpUbDjGMXT-Tqz2K2NaTO-ZEC4viCYswY,339
numpy/core/umath.py,sha256=hMVmNrICdqXRiiRG7UMV0Gr-9xYqJGmkONGQn20iK98,319
numpy/ctypeslib/__init__.py,sha256=WFwMhpV2LJP-IQOspaInhV8c6XPKZwqppE-cvtIpqvU,193
numpy/ctypeslib/__init__.pyi,sha256=R0tHAk1P0jw-HLYjjKBqXEjDyXhByrtbjrgOxht9tE4,619
numpy/ctypeslib/__pycache__/__init__.cpython-313.pyc,,
numpy/ctypeslib/__pycache__/_ctypeslib.cpython-313.pyc,,
numpy/ctypeslib/_ctypeslib.py,sha256=NtEUpisQhDfETBLAkqYf7Ajq0xiNhZurb5SmGGH54pA,19079
numpy/ctypeslib/_ctypeslib.pyi,sha256=xS-NLEO6xwjUr-AUWfGxz3N7X5jwIGBVl6RhOUUYZ74,8084
numpy/doc/__pycache__/ufuncs.cpython-313.pyc,,
numpy/doc/ufuncs.py,sha256=9xt8H34GhrXrFq9cWFUGvJFePa9YuH9Tq1DzAnm2E2E,5414
numpy/dtypes.py,sha256=zuPwgC0ijF2oDRAOJ6I9JKhaJuhXFAygByLQaoVtT54,1312
numpy/dtypes.pyi,sha256=sNN4kzUfhArHuKaMRKofBNZ57trl35UaZ51oDWrMmJ4,15544
numpy/exceptions.py,sha256=x1z7C2RjrDFW8tLewbZjyMiQok0WBm5kKuRPIxVLUjg,7800
numpy/exceptions.pyi,sha256=MJbCHjwFGps97WaVOPkaoUb8wi-l5OUbcFHdWZgBGbI,751
numpy/f2py/__init__.py,sha256=cAgUHWgJQZZsfv8co8KBNr_m8B6fpzdBaUNvJeBf_No,2448
numpy/f2py/__init__.pyi,sha256=UbgqGZKYnDHGHX9MlwBB3aBZ2T470ojrNREIhkwt6gc,132
numpy/f2py/__main__.py,sha256=6i2jVH2fPriV1aocTY_dUFvWK18qa-zjpnISA-OpF3w,130
numpy/f2py/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/__pycache__/__main__.cpython-313.pyc,,
numpy/f2py/__pycache__/__version__.cpython-313.pyc,,
numpy/f2py/__pycache__/_isocbind.cpython-313.pyc,,
numpy/f2py/__pycache__/_src_pyf.cpython-313.pyc,,
numpy/f2py/__pycache__/auxfuncs.cpython-313.pyc,,
numpy/f2py/__pycache__/capi_maps.cpython-313.pyc,,
numpy/f2py/__pycache__/cb_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/cfuncs.cpython-313.pyc,,
numpy/f2py/__pycache__/common_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/crackfortran.cpython-313.pyc,,
numpy/f2py/__pycache__/diagnose.cpython-313.pyc,,
numpy/f2py/__pycache__/f2py2e.cpython-313.pyc,,
numpy/f2py/__pycache__/f90mod_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/func2subr.cpython-313.pyc,,
numpy/f2py/__pycache__/rules.cpython-313.pyc,,
numpy/f2py/__pycache__/symbolic.cpython-313.pyc,,
numpy/f2py/__pycache__/use_rules.cpython-313.pyc,,
numpy/f2py/__version__.py,sha256=99S6mSevuhwGmO9ku--7VUJekhN0ot4-J0cZKiHcqpw,48
numpy/f2py/__version__.pyi,sha256=L4V6f6B-wuPi82B0MzeQsgN0NuHUQs9rKYl1jy3tG7s,45
numpy/f2py/_backends/__init__.py,sha256=7_bA7c_xDpLc4_8vPfH32-Lxn9fcUTgjQ25srdvwvAM,299
numpy/f2py/_backends/__init__.pyi,sha256=i4XhDRwbrl0ta6QGJPxhYGfSgugNGdtoWf1_27eSd60,136
numpy/f2py/_backends/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_backend.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_distutils.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_meson.cpython-313.pyc,,
numpy/f2py/_backends/_backend.py,sha256=oFXZ8-VwcQSbltl8_pgWLPqCOZ8Y_px7oeTk_BlxJTc,1151
numpy/f2py/_backends/_backend.pyi,sha256=sU4YiHvGfMkzDFbhZqqQPT-kwJZsWpGemkLxDion7ss,1342
numpy/f2py/_backends/_distutils.py,sha256=hET0WB4qy-D4BznekGAWhk945k5weq2lGUDR6hriXMo,2385
numpy/f2py/_backends/_distutils.pyi,sha256=-L8K1KQShPGGd1vgr4DlnYf6AshHFaRzAcgGqKv205g,463
numpy/f2py/_backends/_meson.py,sha256=VouUQkWRUk74WhDtkf6HR79QoK-Wrx8E7qO7gVpyDnk,8107
numpy/f2py/_backends/_meson.pyi,sha256=wvYtBdippKeiSeLzaYKehql0_3ThS8T8Aqat03hhjQ4,1869
numpy/f2py/_backends/meson.build.template,sha256=hQeTapAY0xtni5Li-QaEtWx9DH9WDKah2lcEuSZfLLo,1599
numpy/f2py/_isocbind.py,sha256=zaBgpfPNRmxVG3doUIlbZIiyB990MsXiwDabrSj9HnQ,2360
numpy/f2py/_isocbind.pyi,sha256=KuzqHJQk0YSQnRnb8xqnyh8T0DGNnDD6bNI880tadCY,339
numpy/f2py/_src_pyf.py,sha256=PHpo9D28Kq3q_3-KFX8D3sFD9eX8A1c3LuLNzXzByOw,7695
numpy/f2py/_src_pyf.pyi,sha256=9NKnovhbLibbQkjCrRnyiTPDw3MBqycOHl1--BNrIqw,1012
numpy/f2py/auxfuncs.py,sha256=dnaUwrdAv4-LbEiHNbS1vrjQNCO0lBuyWkj3Rt_UizE,26920
numpy/f2py/auxfuncs.pyi,sha256=7RUoWWaHrqSYEmdNd5zCNnmbjUYE5pCe0FCxMXejbhg,8011
numpy/f2py/capi_maps.py,sha256=7C-NndI2UbStNGXbhgbWOmr9tLAxfQvw1zf7Z7w5SFk,30079
numpy/f2py/capi_maps.pyi,sha256=pR0pVZhUxaCpctq7FOWFSAGI_gaLdE-NWAyT96cWWZg,1066
numpy/f2py/cb_rules.py,sha256=6KbPu9yfJ-7pAa24Ij9H34Ll15Qc8CXTqCFiUJI6R8Y,25051
numpy/f2py/cb_rules.pyi,sha256=X_it8-Q0188EDlXd-QxhRdc3OUoA2t6V_jgM5TiQC88,495
numpy/f2py/cfuncs.py,sha256=4J4P12oGpyWZHb1AVKAl7YJ3QUgngwGMCnB1IhrJn7U,52660
numpy/f2py/cfuncs.pyi,sha256=EiAtSQxw4x-UlxsGKIEOJnld1d7dNYrk0bt_rlqLSp0,802
numpy/f2py/common_rules.py,sha256=_9yzIolJMGgpd3D94LdBsODnfUskMRgt2v03rECIHJQ,5030
numpy/f2py/common_rules.pyi,sha256=1uzTkcwiin6dVBbWUiOVB1ZppjKBHoRHG_Byvw-1UbI,323
numpy/f2py/crackfortran.py,sha256=vbAvWj6XszLS-nU0nOedaNNtwtqvkkM8gqZAP9MvPBI,146879
numpy/f2py/crackfortran.pyi,sha256=AvV_KPeE9jLG9EdmPdb2u7-gPJXc1H2yWVmmihHzCgM,10276
numpy/f2py/diagnose.py,sha256=YWNj1vM68e47Lb270wlZk5yrcU-yTlzGaYNPBZ7nTAU,5075
numpy/f2py/diagnose.pyi,sha256=ZFVCWTwf_xzL736p9FcfCYWftOXcNqSMCmq-K27KNN8,23
numpy/f2py/f2py2e.py,sha256=krSW4RpZPDHNX2IWLdn28KWzj0lzFNSc_6fScbGQMfI,28763
numpy/f2py/f2py2e.pyi,sha256=Qt6ZeOYBugJLFpAY3F9K_4hcm0sZt_3APTtdKLKObWA,2153
numpy/f2py/f90mod_rules.py,sha256=7Z5vorU4whX405xML66hr4i1icCUc9gr6an4R-AMh7M,9810
numpy/f2py/f90mod_rules.pyi,sha256=r6w0DuH2Jdt8wPdDYAnXZAQmytIYUqPOxVz-QaWwt74,451
numpy/f2py/func2subr.py,sha256=9igCMMDttIgF1MG6kBOagkjI_SF-UlGjACAj3Ncv0-o,10049
numpy/f2py/func2subr.pyi,sha256=-MDbOrhanuizf3rlcwBQooCF4GnoGprA8ypeFV_m8d0,386
numpy/f2py/rules.py,sha256=Irj-13oLGowNHYElFV-TZUs0VEd0NQpRsnomnI1NTx8,63091
numpy/f2py/rules.pyi,sha256=9GfFmNA8Unlg3pxcGwqwFl7yeKyIcTmx7wiPuiBAT-k,1326
numpy/f2py/setup.cfg,sha256=Fpn4sjqTl5OT5sp8haqKIRnUcTPZNM6MIvUJBU7BIhg,48
numpy/f2py/src/fortranobject.c,sha256=kLiHOty8fUruzfOmL5MQeVNFJSGHBjn7W6QbPYgQb30,46356
numpy/f2py/src/fortranobject.h,sha256=7cfRN_tToAQ1Na13VQ2Kzb2ujMHUAgGsbScnfLVOHqs,5823
numpy/f2py/symbolic.py,sha256=UuFs411WYSqR7JfbsuyNv__IC9wKqxQAWoWRDeKPcdw,53214
numpy/f2py/symbolic.pyi,sha256=piZrats8SXrOD1qEADo-mbsc5NZOIaZ27Fl3d3cydTc,6083
numpy/f2py/tests/__init__.py,sha256=pdPbCTRwpCJamlyvIi9HZTlqAvK5HPbGu3oMA0cu2Rs,329
numpy/f2py/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_abstract_interface.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_array_from_pyobj.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_assumed_shape.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_block_docstring.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_callback.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_common.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_crackfortran.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_data.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_docs.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_f2cmap.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_f2py2e.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_isoc.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_kind.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_mixed.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_modules.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_parameter.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_pyf_src.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_quoted_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_complex.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_integer.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_logical.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_real.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_routines.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_semicolon_split.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_size.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_string.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_symbolic.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_value_attrspec.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/util.cpython-313.pyc,,
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=JFU2w98cB_XNwfrqNtI0yDTmpEdxYO_UEl2pgI_rnt8,658
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=gvQJIzNtvacWE0dhysxn30-iUeI65Hpq7DiE9oRauz8,105
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=s6XLwujiCr6Xi8yBkvLPBXRmo2WsGVohU7K9ALnKUng,7478
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=But9r9m4iL7EGq_haMW8IiQ4VivH0TgUozxX4pPvdpE,29
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=oBwbGSlbr9MkFyhVO2aldjc01dr9GHrMrSiRQek8U64,460
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=rfzw3QdI-eaDSl-hslCgGpd5tHftJOVhXvb21Y9Gf6M,499
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=rmT9k4jP9Ru1PLcGqepw9Jc6P9XNXM0axY7o4hi9lUw,269
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=r08JeTVmTTExA-hYZ6HzaxVwBn1GMbPAuuwBhBDtJUk,130
numpy/f2py/tests/src/block_docstring/foo.f,sha256=y7lPCPu7_Fhs_Tf2hfdpDQo1bhtvNSKRaZAOpM_l3dg,97
numpy/f2py/tests/src/callback/foo.f,sha256=C1hjfpRCQWiOVVzIHqnsYcnLrqQcixrnHCn8hd9GhVk,1254
numpy/f2py/tests/src/callback/gh17797.f90,sha256=_Nrl0a2HgUbtymGU0twaJ--7rMa1Uco2A3swbWvHoMo,148
numpy/f2py/tests/src/callback/gh18335.f90,sha256=NraOyKIXyvv_Y-3xGnmTjtNjW2Znsnlk8AViI8zfovc,506
numpy/f2py/tests/src/callback/gh25211.f,sha256=a2sxlQhtDVbYn8KOKHUYqwc-aCFt7sDPSnJsXFG35uI,179
numpy/f2py/tests/src/callback/gh25211.pyf,sha256=FWxo0JWQlw519BpZV8PoYeI_FZ_K6C-3Wk6gLrfBPlw,447
numpy/f2py/tests/src/callback/gh26681.f90,sha256=-cD69x7omk5wvVsfMHlXiZ-pTcaxs2Bl5G9GHA4UJ2M,566
numpy/f2py/tests/src/cli/gh_22819.pyf,sha256=5rvOfCv-wSosB354LC9pExJmMoSHnbGZGl_rtA2fogA,142
numpy/f2py/tests/src/cli/hi77.f,sha256=ttyI6vAP3qLnDqy82V04XmoqrXNM6uhMvvLri2p0dq0,71
numpy/f2py/tests/src/cli/hiworld.f90,sha256=QWOLPrTxYQu1yrEtyQMbM0fE9M2RmXe7c185KnD5x3o,51
numpy/f2py/tests/src/common/block.f,sha256=GQ0Pd-VMX3H3a-__f2SuosSdwNXHpBqoGnQDjf8aG9g,224
numpy/f2py/tests/src/common/gh19161.f90,sha256=BUejyhqpNVfHZHQ-QC7o7ZSo7lQ6YHyX08lSmQqs6YM,193
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=-5Din7YlY1TU7tUHD2p-_DSTxGBpDsWYNeT9WOwGhno,208
numpy/f2py/tests/src/crackfortran/common_with_division.f,sha256=2LfRa26JEB07_ti-WDmIveq991PxRlL_K6ss28rZDkk,494
numpy/f2py/tests/src/crackfortran/data_common.f,sha256=ZSUAh3uhn9CCF-cYqK5TNmosBGPfsuHBIEfudgysun4,193
numpy/f2py/tests/src/crackfortran/data_multiplier.f,sha256=jYrJKZWF_59JF9EMOSALUjn0UupWvp1teuGpcL5s1Sc,197
numpy/f2py/tests/src/crackfortran/data_stmts.f90,sha256=19YO7OGj0IksyBlmMLZGRBQLjoE3erfkR4tFvhznvvE,693
numpy/f2py/tests/src/crackfortran/data_with_comments.f,sha256=hoyXw330VHh8duMVmAQZjr1lgLVF4zFCIuEaUIrupv0,175
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=CaH7mnWTG7FcnJe2vXN_0zDbMadw6NCqK-JJ2HmDjK8,128
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=jJly1AzF5L9VxbVQ0vr-sf4LaUo4eQzJguhuemFxnvg,375
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=7K5dtOXGuBDAENPNCt-tAGJqTfNKz5OsqVSk16_e7Es,340
numpy/f2py/tests/src/crackfortran/gh22648.pyf,sha256=qZHPRNQljIeYNwbqPLxREnOrSdVV14f3fnaHqB1M7c0,241
numpy/f2py/tests/src/crackfortran/gh23533.f,sha256=w3tr_KcY3s7oSWGDmjfMHv5h0RYVGUpyXquNdNFOJQg,126
numpy/f2py/tests/src/crackfortran/gh23598.f90,sha256=41W6Ire-5wjJTTg6oAo7O1WZfd1Ug9vvNtNgHS5MhEU,101
numpy/f2py/tests/src/crackfortran/gh23598Warn.f90,sha256=1v-hMCT_K7prhhamoM20nMU9zILam84Hr-imck_dYYk,205
numpy/f2py/tests/src/crackfortran/gh23879.f90,sha256=LWDJTYR3t9h1IsrKC8dVXZlBfWX7clLeU006X6Ow8oI,332
numpy/f2py/tests/src/crackfortran/gh27697.f90,sha256=bbnKpDsOuCWluoNodxzCspUQnu169zKTsn4fLTkhwpM,364
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=gPNasx98SIf7Z9ibk_DHiGKCvl7ERtsfoGXiFDT7FbM,282
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=-Fc-qjW1wBr3Dkvdd5dMTrt0hnjnV-1AYo-NFWcwFSo,1184
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=7bubZGMIn7iD31wDkjF1TlXCUM7naCIK69M9d0e3y-U,174
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=Pnwyf56Qd6W3FUH-ZMgnXEYkb7gn18ptNTdwmGan0Jo,167
numpy/f2py/tests/src/crackfortran/pubprivmod.f90,sha256=eYpJwBYLKGOxVbKgEqfny1znib-b7uYhxcRXIf7uwXg,165
numpy/f2py/tests/src/crackfortran/unicode_comment.f90,sha256=aINLh6GlfTwFewxvDoqnMqwuCNb4XAqi5Nj5vXguXYs,98
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=iUOtfHd3OuT1Rz2-yiSgt4uPKGvCt5AzQ1iygJt_yjg,82
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=iJCD8a8MUTmuPuedbcmxW54Nr4alYuLhksBe1sHS4K0,298
numpy/f2py/tests/src/isocintrin/isoCtests.f90,sha256=jcw-fzrFh0w5U66uJYfeUW4gv94L5MnWQ_NpsV9y0oI,998
numpy/f2py/tests/src/kind/foo.f90,sha256=zIHpw1KdkWbTzbXb73hPbCg4N2Htj3XL8DIwM7seXpo,347
numpy/f2py/tests/src/mixed/foo.f,sha256=90zmbSHloY1XQYcPb8B5d9bv9mCZx8Z8AMTtgDwJDz8,85
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=pxKuPzxF3Kn5khyFq9ayCsQiolxB3SaNtcWaK5j6Rv4,179
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=fIQ71wrBc00JUAVUj_r3QF9SdeNniBiMw6Ly7CGgPWU,139
numpy/f2py/tests/src/modules/gh25337/data.f90,sha256=9Uz8CHB9i3_mjC3cTOmkTgPAF5tWSwYacG3MUrU-SY0,180
numpy/f2py/tests/src/modules/gh25337/use_data.f90,sha256=WATiDGAoCKnGgMzm_iMgmfVU0UKOQlk5Fm0iXCmPAkE,179
numpy/f2py/tests/src/modules/gh26920/two_mods_with_no_public_entities.f90,sha256=c7VU4SbK3yWn-6wksP3tDx_Hxh5u_g8UnlDpjU_-tBg,402
numpy/f2py/tests/src/modules/gh26920/two_mods_with_one_public_routine.f90,sha256=eEU7RgFPh-TnNXEuJFdtJmTF-wPnpbHLQhG4fEeJnag,403
numpy/f2py/tests/src/modules/module_data_docstring.f90,sha256=tDZ3fUlazLL8ThJm3VwNGJ75QIlLcW70NnMFv-JA4W0,224
numpy/f2py/tests/src/modules/use_modules.f90,sha256=UsFfx0B2gu_tS-H-BpLWed_yoMDl1kbydMIOz8fvXWA,398
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=fdOPhRi7ipygwYCXcda7p_dlrws5Hd2GlpF9EZ-qnck,157
numpy/f2py/tests/src/parameter/constant_array.f90,sha256=KRg7Gmq_r3B7t3IEgRkP1FT8ve8AuUFWT0WcTlXoN5U,1468
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=-bBf2eqHb-uFxgo6Q7iAtVUUQzrGFqzhHDNaxwSICfQ,1939
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=re7pfzcuaquiOia53UT7qNNrTYu2euGKOF4IhoLmT6g,469
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=nEmMLitKoSAG7gBBEQLWumogN-KS3DBZOAZJWcSDnFw,612
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=IcxESVLKJUZ1k9uYKoSb8Hfm9-O_4rVnlkiUU2diy8Q,609
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=quNbDsM1Ts2rN4WtPO67S9Xi_8l2cXabWRO00CPQSSQ,610
numpy/f2py/tests/src/quoted_character/foo.f,sha256=WjC9D9171fe2f7rkUAZUvik9bkIf9adByfRGzh6V0cM,482
numpy/f2py/tests/src/regression/AB.inc,sha256=cSNxitwrjTKMiJzhY2AI5FaXJ5y9zDgA27x79jyoI6s,16
numpy/f2py/tests/src/regression/assignOnlyModule.f90,sha256=c9RvUP1pQ201O_zOXgV0xp_aJF_8llxuA8Uot9z5tr0,608
numpy/f2py/tests/src/regression/datonly.f90,sha256=9cVvl8zlAuGiqbSHMFzFn6aNWXj2v7sHJdd9A1Oc0qg,392
numpy/f2py/tests/src/regression/f77comments.f,sha256=bqTsmO8WuSLVFsViIV7Nj7wQbJoZ7IAA3d2tpRDKsnA,626
numpy/f2py/tests/src/regression/f77fixedform.f95,sha256=hcLZbdozMJ3V9pByVRp3RoeUvZgLMRLFctpZvxK2hTI,139
numpy/f2py/tests/src/regression/f90continuation.f90,sha256=_W1fj0wXLqT91Q14qpBnM3F7rJKaiSR8upe0mR6_OIE,276
numpy/f2py/tests/src/regression/incfile.f90,sha256=i7Y1zgMXR9bSxnjeYWSDGeCfsS5jiyn7BLb-wbwjz2U,92
numpy/f2py/tests/src/regression/inout.f90,sha256=CpHpgMrf0bqA1W3Ozo3vInDz0RP904S7LkpdAH6ODck,277
numpy/f2py/tests/src/regression/lower_f2py_fortran.f90,sha256=CMQL5RWf9LKnnUDiS-IYa9xc9DGanCYraNq0vGmunOE,100
numpy/f2py/tests/src/regression/mod_derived_types.f90,sha256=565plqPwWDgnkpSb4-cfZbf3wTM85F2Gocklx5wpGWA,567
numpy/f2py/tests/src/return_character/foo77.f,sha256=WzDNF3d_hUDSSZjtxd3DtE-bSx1ilOMEviGyYHbcFgM,980
numpy/f2py/tests/src/return_character/foo90.f90,sha256=ULcETDEt7gXHRzmsMhPsGG4o3lGrcx-FEFaJsPGFKyA,1248
numpy/f2py/tests/src/return_complex/foo77.f,sha256=8ECRJkfX82oFvGWKbIrCvKjf5QQQClx4sSEvsbkB6A8,973
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=c1BnrtWwL2dkrTr7wvlEqNDg59SeNMo3gyJuGdRwcDw,1238
numpy/f2py/tests/src/return_integer/foo77.f,sha256=_8k1evlzBwvgZ047ofpdcbwKdF8Bm3eQ7VYl2Y8b5kA,1178
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=bzxbYtofivGRYH35Ang9ScnbNsVERN8-6ub5-eI-LGQ,1531
numpy/f2py/tests/src/return_logical/foo77.f,sha256=FxiF_X0HkyXHzJM2rLyTubZJu4JB-ObLnVqfZwAQFl8,1188
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=9KmCe7yJYpi4ftkKOM3BCDnPOdBPTbUNrKxY3p37O14,1531
numpy/f2py/tests/src/return_real/foo77.f,sha256=ZTrzb6oDrIDPlrVWP3Bmtkbz3ffHaaSQoXkfTGtCuFE,933
numpy/f2py/tests/src/return_real/foo90.f90,sha256=gZuH5lj2lG6gqHlH766KQ3J4-Ero-G4WpOOo2MG3ohU,1194
numpy/f2py/tests/src/routines/funcfortranname.f,sha256=oGPnHo0zL7kjFnuHw41mWUSXauoeRVPXnYXBb2qljio,123
numpy/f2py/tests/src/routines/funcfortranname.pyf,sha256=coD8AdLyPK4_cGvQJgE2WJW_jH8EAulZCsMeb-Q1gOk,440
numpy/f2py/tests/src/routines/subrout.f,sha256=RTexoH7RApv_mhu-RcVwyNiU-DXMTUP8LJAMSn2wQjk,90
numpy/f2py/tests/src/routines/subrout.pyf,sha256=c9qv4XtIh4wA9avdkDJuXNwojK-VBPldrNhxlh446Ic,322
numpy/f2py/tests/src/size/foo.f90,sha256=IlFAQazwBRr3zyT7v36-tV0-fXtB1d7WFp6S1JVMstg,815
numpy/f2py/tests/src/string/char.f90,sha256=ihr_BH9lY7eXcQpHHDQhFoKcbu7VMOX5QP2Tlr7xlaM,618
numpy/f2py/tests/src/string/fixed_string.f90,sha256=5n6IkuASFKgYICXY9foCVoqndfAY0AQZFEK8L8ARBGM,695
numpy/f2py/tests/src/string/gh24008.f,sha256=UA8Pr-_yplfOFmc6m4v9ryFQ8W9OulaglulefkFWD68,217
numpy/f2py/tests/src/string/gh24662.f90,sha256=-Tp9Kd1avvM7AIr8ZukFA9RVr-wusziAnE8AvG9QQI4,197
numpy/f2py/tests/src/string/gh25286.f90,sha256=2EpxvC-0_dA58MBfGQcLyHzpZgKcMf_W9c73C_Mqnok,304
numpy/f2py/tests/src/string/gh25286.pyf,sha256=GjgWKh1fHNdPGRiX5ek60i1XSeZsfFalydWqjISPVV8,381
numpy/f2py/tests/src/string/gh25286_bc.pyf,sha256=6Y9zU66NfcGhTXlFOdFjCSMSwKXpq5ZfAe3FwpkAsm4,384
numpy/f2py/tests/src/string/scalar_string.f90,sha256=ACxV2i6iPDk-a6L_Bs4jryVKYJMEGUTitEIYTjbJes4,176
numpy/f2py/tests/src/string/string.f,sha256=shr3fLVZaa6SyUJFYIF1OZuhff8v5lCwsVNBU2B-3pk,248
numpy/f2py/tests/src/value_attrspec/gh21665.f90,sha256=JC0FfVXsnB2lZHb-nGbySnxv_9VHAyD0mKaLDowczFU,190
numpy/f2py/tests/test_abstract_interface.py,sha256=PXNQB0DZdmdZyysJkB8f9GY0_hA3hGkmha8aQBXc1Sk,811
numpy/f2py/tests/test_array_from_pyobj.py,sha256=N1RJ0yFcLs6cFmdxSjizjfLRTEhdKRhrO9Vx8bcG0GU,23696
numpy/f2py/tests/test_assumed_shape.py,sha256=8kPoQWn6IfMWNMba0al7a5XopKb3JnvZP3V3P6O2F8o,1467
numpy/f2py/tests/test_block_docstring.py,sha256=P3K0QqnY0UfUQPc3vDrlP_WlZ6gNJ7iokG-D-ZG9tXQ,584
numpy/f2py/tests/test_callback.py,sha256=P_5qM1xWOYfjeDgd70cIVpV1h0_tA1AP3kxRZDAeqII,7099
numpy/f2py/tests/test_character.py,sha256=R6FhfIi85E6L1qwlJtsnTCvNgFRriE3kSXefTwIVgLk,21931
numpy/f2py/tests/test_common.py,sha256=gr4MF659JBWvSY4eQAqgHnOrVbEpq0ZhGM5Cdbye1L4,644
numpy/f2py/tests/test_crackfortran.py,sha256=x_E4KmEfBX5SFsNkO_-mUi4W_WuzB-ZFsLOfUdHjLVE,16413
numpy/f2py/tests/test_data.py,sha256=tete-xcIZHZi5VFjy_pyTjr5AjhQzoyJvLsT9QLYU1M,2895
numpy/f2py/tests/test_docs.py,sha256=wGsRmCJugExEAvj25pANoLr45S6fkpG4kf47dnfg9Ew,1855
numpy/f2py/tests/test_f2cmap.py,sha256=zM8lksGAoH-cRvEVRkzciZ4oqH28obd-vvMVUObVjt0,387
numpy/f2py/tests/test_f2py2e.py,sha256=aGZnZH5USd8FJpG5F1L6bWfUzuUqP954lit5-TDPbeE,27834
numpy/f2py/tests/test_isoc.py,sha256=g5PLyJuAYwF0obaZ55j_e-CNOODJcADsYFSfxcCl5LM,1434
numpy/f2py/tests/test_kind.py,sha256=ovQVxbtbbnb-Keo8Dh2LpDyPLbIA1uxiZOzMLo5KMX0,1825
numpy/f2py/tests/test_mixed.py,sha256=DZcTCCle0o4aopFmGi58KtxzP6NFFci4N-pL3_HLb90,862
numpy/f2py/tests/test_modules.py,sha256=GaOwxLf8KLdNkWIl9fveT9xg_wvCFdDsel9QiFweCAE,2301
numpy/f2py/tests/test_parameter.py,sha256=P8hDezlxKN_Cm06oWGkS0pwlJvQz5QYwBsyTEA_Y1PQ,4634
numpy/f2py/tests/test_pyf_src.py,sha256=xV81hRiGeytFFKeVnp-6O2OrGVdzJyecMEalCQSoDoI,1134
numpy/f2py/tests/test_quoted_character.py,sha256=x19FhD6ZA7JkDuLuiXi48sGd8b42SPRuwwEY8CVRb24,477
numpy/f2py/tests/test_regression.py,sha256=APQz3e38jz-AbGEBN5n-P1Wuegx4Da1ze7D7nLLpUL8,6197
numpy/f2py/tests/test_return_character.py,sha256=t8cxO8LatnBXf2EU-HkfmdxvdHMYDk9DLx3kNUTArC4,1534
numpy/f2py/tests/test_return_complex.py,sha256=_uWrnSh-IDL8men8X__5srP4wM0RkICr4WVJgoNgrzY,2440
numpy/f2py/tests/test_return_integer.py,sha256=ng_cpFX4nStcpSFoYdD9GiUdCJSXPU0On2MLOA4uOpQ,1813
numpy/f2py/tests/test_return_logical.py,sha256=OrS11uAw_asDamL7inRKf-S-7SBG0GTS8Vrqlexrkm0,2048
numpy/f2py/tests/test_return_real.py,sha256=ynInWwkcRfUe981kGJnrkkZeKK7QFlvkiODoIJj6Jg0,3273
numpy/f2py/tests/test_routines.py,sha256=f9pR8FNJgKuBWtzCjlfniWVHJecpW6gSNkGDb2t693c,795
numpy/f2py/tests/test_semicolon_split.py,sha256=akc4xJiHI6xOCfpCEtFYPMz8qy2K5jODEPyJHYQvLdE,1627
numpy/f2py/tests/test_size.py,sha256=SjES727lNcCJFePDnh7uBhncOXWOcqHqVPbZPvBO5js,1155
numpy/f2py/tests/test_string.py,sha256=47wYPuO1NkjhXSbbyS8vBKsNCju5dA9uMjNhGPx-BGg,2938
numpy/f2py/tests/test_symbolic.py,sha256=dmuYLhhcv-rT-ux_aVrWaJj_Yxmznezl6Enu8-ediK0,18342
numpy/f2py/tests/test_value_attrspec.py,sha256=4wY9qPXl0JoPGCG7GyyuMDKLfsHAV8KRWGdEk9-ZZT8,330
numpy/f2py/tests/util.py,sha256=KIDsCW5uZXe6jSdWpY9Ozlqs5-v-eeDsW3P5TDWKDzo,12112
numpy/f2py/use_rules.py,sha256=emZhSLPbNDyBHnsfKKXDnGz4P_gwrgL0dfCZcD3n9D4,3376
numpy/f2py/use_rules.pyi,sha256=gIAAemWfcidclVYZUpa6RRmSdUEDw4FDnGPaCNo93Zw,424
numpy/fft/__init__.py,sha256=OWE0m6H_blyv1wtqQpiXU5kqxF6O2UxxcV5t11U05RE,8291
numpy/fft/__init__.pyi,sha256=6XgAsd9coqJ3jBOPD3vn1-8AcbMLhjxzQd21xjeqmlA,514
numpy/fft/__pycache__/__init__.cpython-313.pyc,,
numpy/fft/__pycache__/_helper.cpython-313.pyc,,
numpy/fft/__pycache__/_pocketfft.cpython-313.pyc,,
numpy/fft/__pycache__/helper.cpython-313.pyc,,
numpy/fft/_helper.py,sha256=hIn2ZyEYG4fLB3MGvCPvpSrLXFfh-xO4zGKljk_TQjY,6787
numpy/fft/_helper.pyi,sha256=1A1kitc5k62ER6X1XLF7PIQL5FiVxxRKu_iCqiQ1kIU,1394
numpy/fft/_pocketfft.py,sha256=CfpApR9R0SOucql9gp9vXadm_y5cBM-Xnj5trDpvFSE,62598
numpy/fft/_pocketfft.pyi,sha256=_RIRwdhtixjN4qszZk-xeYn2jmcW_NNAMEJHeETigv0,3174
numpy/fft/_pocketfft_umath.cpython-313-darwin.so,sha256=nRuakP32qX3EBQ16amfWG2TapR2UAXF5GgtTaBZTkfM,329768
numpy/fft/helper.py,sha256=RoEADsOnoCgSTL1gE5n-36llz8iwxGzn52af3L-9KEY,611
numpy/fft/helper.pyi,sha256=KsF45bVyZ4_eJbBFpkER9L8MCWmg7dJuhLqY_7uFNZs,891
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/fft/tests/__pycache__/test_helper.cpython-313.pyc,,
numpy/fft/tests/__pycache__/test_pocketfft.cpython-313.pyc,,
numpy/fft/tests/test_helper.py,sha256=LeVDCCdHzFhmCQ5ByMtVyA22GphgTQS5dupuxrLE8X0,6154
numpy/fft/tests/test_pocketfft.py,sha256=PCF833rSWsXOMWN8wCluhq0aYHU24_tHbuMl1PuO6dE,24446
numpy/lib/__init__.py,sha256=zYGuqEfPqq7LDbidpxYs8GgCNAmoJ4xQgFvF3XKJ5Rg,3004
numpy/lib/__init__.pyi,sha256=Z7OsQAZGURd4cI3xnEF37unbOUqtknwEkT8yQTF-AF8,1651
numpy/lib/__pycache__/__init__.cpython-313.pyc,,
numpy/lib/__pycache__/_array_utils_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arraypad_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arraysetops_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arrayterator_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_datasource.cpython-313.pyc,,
numpy/lib/__pycache__/_format_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_function_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_histograms_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_index_tricks_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_iotools.cpython-313.pyc,,
numpy/lib/__pycache__/_nanfunctions_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_npyio_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_polynomial_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_scimath_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_shape_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_stride_tricks_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_twodim_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_type_check_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_ufunclike_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_user_array_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_utils_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_version.cpython-313.pyc,,
numpy/lib/__pycache__/array_utils.cpython-313.pyc,,
numpy/lib/__pycache__/format.cpython-313.pyc,,
numpy/lib/__pycache__/introspect.cpython-313.pyc,,
numpy/lib/__pycache__/mixins.cpython-313.pyc,,
numpy/lib/__pycache__/npyio.cpython-313.pyc,,
numpy/lib/__pycache__/recfunctions.cpython-313.pyc,,
numpy/lib/__pycache__/scimath.cpython-313.pyc,,
numpy/lib/__pycache__/stride_tricks.cpython-313.pyc,,
numpy/lib/__pycache__/user_array.cpython-313.pyc,,
numpy/lib/_array_utils_impl.py,sha256=GYWiyNqLQ7DGUSBXz0bbR6AAqZStDIwUe7tsbZ__15M,1697
numpy/lib/_array_utils_impl.pyi,sha256=AktSeZcFe_XUQ6utYHQyJKG8l8bhM8tQL2Kttj1DjcQ,820
numpy/lib/_arraypad_impl.py,sha256=z5--XT80TcnDZezHVrdxauJSY3yC4vMDdd7JlO-h3zw,32296
numpy/lib/_arraypad_impl.pyi,sha256=W98XPsguuf8B924KVVxs6l_EOBM9JKzwTmHL98CKbs0,1837
numpy/lib/_arraysetops_impl.py,sha256=VFdgpFZJcyJhYFPcTk_LQD_SrqX6poy_shsLKvZigy0,41275
numpy/lib/_arraysetops_impl.pyi,sha256=gYwt9qhpNQf5krbOK7MAHq9IQn1D4bb-lvbBIK98m3s,12803
numpy/lib/_arrayterator_impl.py,sha256=HtOADIHuG9ADbbMTgmh4P_muke1V-8E-FNEO3bVOGPA,7218
numpy/lib/_arrayterator_impl.pyi,sha256=8u0nb5NPpWNib-FlWaXlp6BXBPgTv5__NF30FD_1qmM,1876
numpy/lib/_datasource.py,sha256=zk-Vbn4JlDHEVa3De6A3NgjnnizSJi-HF0ZvvA6YIo4,22731
numpy/lib/_datasource.pyi,sha256=135RvD3p-3mHdNp_sZV4aN9brwEFvEM49VE1eHlFEfs,996
numpy/lib/_format_impl.py,sha256=zcQ3xXxPf7epktsYrcdBbIPuOCh9OPV1g3gB6ghf4rE,36865
numpy/lib/_format_impl.pyi,sha256=_0lEht2hKbTevv0eGChmYMBTAg-2jAfvrfU9p326VHs,869
numpy/lib/_function_base_impl.py,sha256=AZGyN29Ecw4LRuU1TNUcPC7cVHO9ye4bJ9FQI7n_Gwc,196425
numpy/lib/_function_base_impl.pyi,sha256=HY21gmJcUIvTsYL2UUZ8l2MYj34cp_7Mdsmck-FjeEE,24116
numpy/lib/_histograms_impl.py,sha256=Utu7aAQc7ZpsHn_04ogUnZq1ZcdHfipcq9eRq817oVU,38432
numpy/lib/_histograms_impl.pyi,sha256=QouOxW0sa_LMJ2hDv5WEO9k95mTMjEvbP2-7swNJxzI,1093
numpy/lib/_index_tricks_impl.py,sha256=g7Np4E8AG9sgyi9HTUgvOM08pIlAj_cvXw4cc7NrU5I,32186
numpy/lib/_index_tricks_impl.pyi,sha256=gQwY1mj_Sxk2eo9BXcqJ68F88XvzQB81o0nNUkQ9w9o,6325
numpy/lib/_iotools.py,sha256=0jtpvpl5L-_1ODI21F-1i19t1e3L-6wJxRd1CSLewL0,30876
numpy/lib/_iotools.pyi,sha256=69hfBI89W2UP6ozHiSByt-GxTupni-gBRPihFbXSh6Q,3393
numpy/lib/_nanfunctions_impl.py,sha256=cdOT7dYwjvUpI9iEHTrwzbbtKhP9ZZgOCMirTBeYPUk,71949
numpy/lib/_nanfunctions_impl.pyi,sha256=j5dyJz_c-SQDxXrL9N2ouKC-DsP_EVDZyLedGXqCpMI,833
numpy/lib/_npyio_impl.py,sha256=qV8RJEISx8QgbMIPMuy1IDmSU8-kArjBOXO7sg1iJ_M,99280
numpy/lib/_npyio_impl.pyi,sha256=WWlGxbobwLgEiD-k58g_Q9K1HW1vDk--AYrBSjjqALE,9388
numpy/lib/_polynomial_impl.py,sha256=TWiqlG3WDa97tayxQCEltZD9TNhUyFprzL_Umd7Lxso,44134
numpy/lib/_polynomial_impl.pyi,sha256=9PvnPmeCk45ldiJ8xHwsIVdX9DrjPhY9H7CEFbVJMLQ,6999
numpy/lib/_scimath_impl.py,sha256=QAU4uM_INzVqCTs-ATEyy1JhREl_wDJn_ygU75YtfgE,15692
numpy/lib/_scimath_impl.pyi,sha256=pXBZjHPB_FbeBfe9M3N8TjrET_oclGuafWjTHC-xjUs,2774
numpy/lib/_shape_base_impl.py,sha256=5vkU9rPOwKvSc7TzxdfWtM08uV0m15iHPTxbqcY47Oc,39479
numpy/lib/_shape_base_impl.pyi,sha256=36gmgbFd1cUmSUfUihFtb1brc2gKLYi8NXDAEzLyBmQ,5412
numpy/lib/_stride_tricks_impl.py,sha256=y3Uxp3jFzDwmIQ137N2zap7-vW_jONUQmXnbfqrs60A,18025
numpy/lib/_stride_tricks_impl.pyi,sha256=6rR7IO04w1FPCKUM920r9Kf_A_hpZbIABo6Rcl34tFI,1815
numpy/lib/_twodim_base_impl.py,sha256=3nOLvCD6cfM6MD3o381F48GB8poqsUGDCDOQlOBQXmY,33925
numpy/lib/_twodim_base_impl.pyi,sha256=nBRqOTSD21ioBkUw6vtzy1-ZyczJcvybkvG3-hvSIkY,11193
numpy/lib/_type_check_impl.py,sha256=WeVfWz_0Klvb2K_6l0x4nHwHBwPYgfcxeZinV_dp_mw,19221
numpy/lib/_type_check_impl.pyi,sha256=xpZV5LStVGHbEDAcJUbD7iZFE0onwCPZZuwb01P4o_Q,9713
numpy/lib/_ufunclike_impl.py,sha256=0eemf_EYlLmSa4inNr3iuJ1eoTMqLyIR0n6dQymga3Y,6309
numpy/lib/_ufunclike_impl.pyi,sha256=SJ7wbjWFI6WL_rp3CNqbZoKoza4Ou4uDwXvpt4iekys,1288
numpy/lib/_user_array_impl.py,sha256=t3nnrFuvbBizFV1K3C9NNyIM80LU5spA88MlrYJzEok,7697
numpy/lib/_user_array_impl.pyi,sha256=AZpI9fHHYpLxyYL9ud5YDHcZhxLl-YpfB23i9f154BQ,9110
numpy/lib/_utils_impl.py,sha256=7BSreRcHNIsUeMj3U1GbqzVjJYKvyuEWHdG_C4TM46Q,23346
numpy/lib/_utils_impl.pyi,sha256=ckxdUjdGEaa3JAKVQZHYgZ1R3glZZg-ssh90vkV7dJg,371
numpy/lib/_version.py,sha256=4dUrc9Js0KPEQ5adoYKR5dnP4ffjCDtJUKPqcMauwY4,4851
numpy/lib/_version.pyi,sha256=vysY5Vl_nh4si6GkMXEoB6pUDl-jJ5g0LpSDa40F124,641
numpy/lib/array_utils.py,sha256=XbcyhJ9S0IlNnP9Ny6yygLMEACWWUPNOU8vevj1TEpI,144
numpy/lib/array_utils.pyi,sha256=LfY_fzfTdtjoLIi5FSCDsC5weYrmAHFh7fxFfniupbg,296
numpy/lib/format.py,sha256=npJ0eJhT7uKNK5a0lCMGfiJv-R4jyNhiIPeZbJcNXBs,477
numpy/lib/format.pyi,sha256=fh-5SN4MORvjLliV8LwOb3VqG8tFvOaMeG4Vn5CBusA,1482
numpy/lib/introspect.py,sha256=u-wgfMuYt8GI3AnRNdXs4j4w9eNTsazlqrazS-P7gKA,2749
numpy/lib/introspect.pyi,sha256=AWVX6b9mzdwsxizOY0LydWKBEpGatHaeeXGc2txYJEM,152
numpy/lib/mixins.py,sha256=Kff76ScpgWV3cruicI9A7a4zfBnGVmXtwQzMzu5xDEo,7200
numpy/lib/mixins.pyi,sha256=I3iXqrcHpV4jwsgBGJKT2Ero2SlTSEZZDmfcx3DJ7Cc,3131
numpy/lib/npyio.py,sha256=eaPvfHGSzUE70TJHHLOCPIX9G5ihMuBEexy6_PNhJ9Q,68
numpy/lib/npyio.pyi,sha256=qX68dlgy7M2MtAgNSabTV8rWOTXOXCE1_72XcdJq10Y,192
numpy/lib/recfunctions.py,sha256=T4aa5xXav9ntfw5YmzPiq_YUkh12wGk40XyBLQPCEzU,59539
numpy/lib/recfunctions.pyi,sha256=NTf4FyM2Kinx56nNHcyGjKUz_RBSJQr-qtZsLKeIYvQ,13216
numpy/lib/scimath.py,sha256=qjFaQeq0zEIl7gKqOhaj_vmCC_KaFdyTmHdLUUkSp5I,169
numpy/lib/scimath.pyi,sha256=Fe7sfleFSY0uCGUj5gATxjEoMnva1nJ53YyP1wP11Nk,512
numpy/lib/stride_tricks.py,sha256=x0_BfwlycBAlR3BvpxTndeP96dHBT_fASbkTTTzBYgI,88
numpy/lib/stride_tricks.pyi,sha256=FLo0b8NlLPsS58VzjFFchivpBOjjE_meU0EhWEFPQNY,170
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__datasource.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__iotools.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__version.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_array_utils.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arraypad.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arraysetops.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arrayterator.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_format.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_function_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_histograms.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_index_tricks.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_io.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_loadtxt.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_mixins.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_nanfunctions.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_packbits.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_polynomial.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_recfunctions.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_shape_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_stride_tricks.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_twodim_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_type_check.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_ufunclike.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_utils.cpython-313.pyc,,
numpy/lib/tests/data/py2-np0-objarr.npy,sha256=ZLoI7K3iQpXDkuoDF1Ymyc6Jbw4JngbQKC9grauVRsk,258
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=7mtikKlHXp4unZhM8eBot8Cknlx1BofJdd73Np2PW8o,325
numpy/lib/tests/data/py3-objarr.npz,sha256=vVRl9_NZ7_q-hjduUr8YWnzRy8ESNlmvMPlaSSC69fk,453
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=0vL8l30yb53Wwnt0YbdqvOl2xQf9fc0S-0pRTMAdaYc,10581
numpy/lib/tests/test__iotools.py,sha256=LTODsFclDQnIbKQb98hEysgVhQ6cs230aj45pA1QYFc,13765
numpy/lib/tests/test__version.py,sha256=SwXoEqMap603c2jd7ONod0ZOVQeX6T-zArMf03OCHbw,1999
numpy/lib/tests/test_array_utils.py,sha256=hPXtCjoBKe6MP91sg_04EBpRYg7MITVlCAgD1AScjx8,1118
numpy/lib/tests/test_arraypad.py,sha256=GzqMIQ0Y8XLYmP5osXzl5W1Pcywy_OK-39STKoCWJc4,56155
numpy/lib/tests/test_arraysetops.py,sha256=GKotFUbKgEfHybghYP1zIM0RWMqW1pa4cdYlML1seXQ,40445
numpy/lib/tests/test_arrayterator.py,sha256=1LZmgQQJpndfwh3X2mL4JpaWvKQl9a0WAnQdSpXimhM,1301
numpy/lib/tests/test_format.py,sha256=BTKd2lUodd8gNznWkh_Hl3mG8Mu8SOFADEqGd5kCw64,41956
numpy/lib/tests/test_function_base.py,sha256=z2SkeGd9qQjXmaxk6bhoi06qlfxdrDzJEqRsDxIuEoM,171119
numpy/lib/tests/test_histograms.py,sha256=QkcA46lJ1Y-T3f4-Qn7kn6J9bIid3RLK7NKMrUI3Rpw,33966
numpy/lib/tests/test_index_tricks.py,sha256=bp4GFjqQ3s_taGDVsCOgs5YU7qtDMhQuPGwvcCxj2sk,20477
numpy/lib/tests/test_io.py,sha256=fpZDI7-blZHJvUq4vrXGuBRxM-cyS3mbF1SEnPMVvzg,110164
numpy/lib/tests/test_loadtxt.py,sha256=1R_xoumDPtPGQYoWh_WWCFKeb3-9WfLIoMHCYQQ0CtQ,40557
numpy/lib/tests/test_mixins.py,sha256=9r6tgP4Wb6vCDn590PkHmHl-GBAoAL6_-mwp2wbiaO0,7009
numpy/lib/tests/test_nanfunctions.py,sha256=1GGtPUD8bS5v2FxLr8e0BUgx9k6Iu-8WLZisawPY4Yw,54098
numpy/lib/tests/test_packbits.py,sha256=REkoSXh9FVVTizyyHWkLqXFLIjt0rynXeixhK8-gBgk,17543
numpy/lib/tests/test_polynomial.py,sha256=3Z7x5gf2cSb5pN5e0Sb_hZetF3mI5GrTLv-OaN7v0m0,12312
numpy/lib/tests/test_recfunctions.py,sha256=xYsC_t_tpIpWJvS1pRU2HNxZTO1cJ3QZ1OnXt4ajm0s,43928
numpy/lib/tests/test_regression.py,sha256=UURtmtwfrxMDF3UY1ZMNbgIJOa38jUzYKCmpYYD8e3Q,7716
numpy/lib/tests/test_shape_base.py,sha256=ZWHeWCs9x0sD-L03h6kTmUdRHvxHVC-8KOu8KomhyKQ,27406
numpy/lib/tests/test_stride_tricks.py,sha256=tBErppWSp8jAckkx_zN5ZbAhfKxZJ99cOQxDI9B_xh0,23030
numpy/lib/tests/test_twodim_base.py,sha256=-djv2iP3W2sB4rAgj9Orl8alGwDFfPvcVu6CNvlKIcg,18925
numpy/lib/tests/test_type_check.py,sha256=2M6uyLSI-CP13CAylnBn3kbT6nrK6wYWW-Scw13vsAQ,14796
numpy/lib/tests/test_ufunclike.py,sha256=5a65WfziLpjPJ_yE8zg-A-q08xlyiU8_S1JH8kb-Uyw,3015
numpy/lib/tests/test_utils.py,sha256=HRZxH8Rs-PxCpMAhgbNOrTfBrsA8B2eTOKypY0Udczw,2374
numpy/lib/user_array.py,sha256=zs6u6TAXoAySGAZc1qE6fKD4AN-t6urZCaiZaKmHiso,63
numpy/lib/user_array.pyi,sha256=8C-aTekEYA0bVU7F3turaw1w0j8FfFvDp9xKa9Pfe94,53
numpy/linalg/__init__.py,sha256=7pVvFwOJFKOArGeUs6MNj3MNqqsx7xx0vt2_7avNAg4,2124
numpy/linalg/__init__.pyi,sha256=C3fZHKPSa4wpfRqfTjw3DpzE5p-Czjus48OuMLsDckQ,1060
numpy/linalg/__pycache__/__init__.cpython-313.pyc,,
numpy/linalg/__pycache__/_linalg.cpython-313.pyc,,
numpy/linalg/__pycache__/linalg.cpython-313.pyc,,
numpy/linalg/_linalg.py,sha256=XSBRz5lsdmZ9olIGvdqGIgnajzI-mSx4eBbKNYsYJ1A,115032
numpy/linalg/_linalg.pyi,sha256=inijXDOFEzZayOL37HNKOqyH8wCLQaU0r__pO4do7Ag,11141
numpy/linalg/_umath_linalg.cpython-313-darwin.so,sha256=bx_jcdHV0lUST0c9oUB8TqPFiu1Vl2bV_6tfgfyBwv8,154752
numpy/linalg/_umath_linalg.pyi,sha256=awvRP1FGuomyfeaR0wzHvrXURAI8tUF3u2RRZ24hkXw,1409
numpy/linalg/lapack_lite.cpython-313-darwin.so,sha256=DJ-ghWSmRYswrF2bkY3aiEgqtJWA2Tqb3noS_asXD3I,52896
numpy/linalg/lapack_lite.pyi,sha256=QjaS8R4uu6MiJDcCFNE5EOAYGnFCcrNz873gs2OUXEM,2672
numpy/linalg/linalg.py,sha256=6NimP68tYa0qBRglWH87_tOh2scshtDpcwfvBvmd6Po,585
numpy/linalg/linalg.pyi,sha256=8E5sbKeM5Ors7r143mM7A4ui8kFZM0SF7NfUGW1eN-4,932
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_linalg.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/linalg/tests/test_deprecations.py,sha256=9p_SRmtxj2zc1doY9Ie3dyy5JzWy-tCQWFoajcAJUmM,640
numpy/linalg/tests/test_linalg.py,sha256=G2gy9cyvTNbVKGRx2hd9ybPL0MFttglJtd3U0bfe1b0,84312
numpy/linalg/tests/test_regression.py,sha256=9a96oyeEGQMUxfw_-GUjNWqn51iu4Cf7kllJ0bKp9ws,6704
numpy/ma/API_CHANGES.txt,sha256=F_4jW8X5cYBbzpcwteymkonTmvzgKKY2kGrHF1AtnrI,3405
numpy/ma/LICENSE,sha256=BfO4g1GYjs-tEKvpLAxQ5YdcZFLVAJoAhMwpFVH_zKY,1593
numpy/ma/README.rst,sha256=krf2cvVK_zNQf1d3yVYwg0uDHzTiR4vHbr91zwaAyoI,9874
numpy/ma/__init__.py,sha256=XpDWYXwauDc49-INsk455D03Uw4p6xFdsdWOn2rt87U,1406
numpy/ma/__init__.pyi,sha256=QV7F1eN7GQLA2V2vI_bYXC_XhoZl-2IqXHWIqJtXLKU,6946
numpy/ma/__pycache__/__init__.cpython-313.pyc,,
numpy/ma/__pycache__/core.cpython-313.pyc,,
numpy/ma/__pycache__/extras.cpython-313.pyc,,
numpy/ma/__pycache__/mrecords.cpython-313.pyc,,
numpy/ma/__pycache__/testutils.cpython-313.pyc,,
numpy/ma/core.py,sha256=Te0RIWw8JyG2iJJjeSiG_t1ahKAICDdr7_pl4G6Q1Yc,288881
numpy/ma/core.pyi,sha256=RxL-vzdzpBB97UqNesAkHjvFxQUom1ARUvKurQgz58I,40459
numpy/ma/extras.py,sha256=f8qf6t_x9k34OKmHiNIft9PFCyLYMeBSGhiYjhUuIpc,70680
numpy/ma/extras.pyi,sha256=4nJDP_0yoEtchWJgczd0ubXba76TsGPBOuCRexQgVbE,3794
numpy/ma/mrecords.py,sha256=00gzzy_xxC408pVZIRUSRhbwqc1UHcyhE-tO2FYM8IE,27073
numpy/ma/mrecords.pyi,sha256=YW81zL9LDzi-L-2WI7135-HxBzj12n4YgARHh2qZ6Bs,1973
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_arrayobject.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_core.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_extras.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_mrecords.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_old_ma.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_subclassing.cpython-313.pyc,,
numpy/ma/tests/test_arrayobject.py,sha256=MSvEcxlsVt4YZ7mVXU8q_hkwM0I7xsxWejEqnUQx6hE,1099
numpy/ma/tests/test_core.py,sha256=novMpyqUqf9O7970aVB2HUqTBSiUqMQINMas3PbTgjM,219717
numpy/ma/tests/test_deprecations.py,sha256=Hye4FMqAdPOOCVnihbs4R8ntLvYJy6WF3LA29876urI,2569
numpy/ma/tests/test_extras.py,sha256=BnFaTx33kNdLDuLJ74Dt1f7gGsD_noYFmBGA8UelUqI,78435
numpy/ma/tests/test_mrecords.py,sha256=ZDEv-LbPlx4Qf9NQs8unNXgrdXupRv4IQljf4_vCr34,19894
numpy/ma/tests/test_old_ma.py,sha256=PMA26SyXJxN0o-pPvyEhl_YF2zRcxuPRMPAXztKCphA,33018
numpy/ma/tests/test_regression.py,sha256=_eskYMrmSHe-_iODK6mvRD5gN_w6NpAl5agsyIGRRUo,3303
numpy/ma/tests/test_subclassing.py,sha256=_TQZ4WM2VG-yuITIXeRZbAZrWDHpxtQoLzDKbGRmuHM,16936
numpy/ma/testutils.py,sha256=vNG1ay689zOktrm-33tyz0bsCLxkJHK6j--2JtHRPq4,10235
numpy/matlib.py,sha256=_S9N8S2NNsHGQUcloxrxABtJDejHiUyMdMJO7SayPkA,10638
numpy/matlib.pyi,sha256=d9Tw-ThrWNUgXKGTiQvCjqrkWQSWqHcXUXAxvYENtYk,9602
numpy/matrixlib/__init__.py,sha256=Ut6IqfjuA-kwwo6HBOYAgFjXXC_h7YV_3HyDsKM72dk,243
numpy/matrixlib/__init__.pyi,sha256=e9xC6kWhIYoPqa3-tmtxdaq8RLjXrBjpyXLqV-pV9UY,106
numpy/matrixlib/__pycache__/__init__.cpython-313.pyc,,
numpy/matrixlib/__pycache__/defmatrix.cpython-313.pyc,,
numpy/matrixlib/defmatrix.py,sha256=wpw6lZU9X6qp8wAJokDXt2RBrL1eXqlmBt-ojIwYzlU,30875
numpy/matrixlib/defmatrix.pyi,sha256=ReQicwbCq4EFGM6paj5KoTeFK3fyiBMC4fJLJcP0SI4,478
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_defmatrix.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_interaction.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_masked_matrix.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_matrix_linalg.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_multiarray.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_numeric.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/matrixlib/tests/test_defmatrix.py,sha256=G9v4-cGuAbHVVDCJ2rCUnQrSTUChOih_6ZMV-ZlYsNA,14977
numpy/matrixlib/tests/test_interaction.py,sha256=BMpaAIeGOJ5EEHWuozBifN8l3Av5RO6jGoaPgdzTiqQ,11874
numpy/matrixlib/tests/test_masked_matrix.py,sha256=UN212xE5e3G9OuwdOWvRMFT5-z3zIfjQQIIpY26a52k,8787
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=33UxWKz2NwI2Wt3pP0AyaooZ5tCFpbOePWek3XT0a4U,2149
numpy/matrixlib/tests/test_multiarray.py,sha256=S5kjzsQR2YgT0qIGrNO1lUDl3o-h0EIdg_g3U3CnuRc,555
numpy/matrixlib/tests/test_numeric.py,sha256=hZ-r921WDG8Ck8KmT6ulgykjHU1QaGY6gprC2OPo-vg,447
numpy/matrixlib/tests/test_regression.py,sha256=XnfZ4RoTS49XMUyUlHVMc6wcWImNRja7DT1wTdEk428,934
numpy/polynomial/__init__.py,sha256=gGSwLNpPCpXfPgiJSsgVoVsJ0AS1c-_MWlGOeiG55sI,6726
numpy/polynomial/__init__.pyi,sha256=tVWqA3_ZzcTyfp5yIr4ca87Tgx4YtY4660UQi3JhfJI,688
numpy/polynomial/__pycache__/__init__.cpython-313.pyc,,
numpy/polynomial/__pycache__/_polybase.cpython-313.pyc,,
numpy/polynomial/__pycache__/chebyshev.cpython-313.pyc,,
numpy/polynomial/__pycache__/hermite.cpython-313.pyc,,
numpy/polynomial/__pycache__/hermite_e.cpython-313.pyc,,
numpy/polynomial/__pycache__/laguerre.cpython-313.pyc,,
numpy/polynomial/__pycache__/legendre.cpython-313.pyc,,
numpy/polynomial/__pycache__/polynomial.cpython-313.pyc,,
numpy/polynomial/__pycache__/polyutils.cpython-313.pyc,,
numpy/polynomial/_polybase.py,sha256=b0kCiTgUm8D5QC_LWSm6yNvwC79npDAeksK0vQPciCQ,39358
numpy/polynomial/_polybase.pyi,sha256=mKbxu6z3iC6NnDNXHPrMm6Vo6RQvrvtCel7S5Mi3Q3Q,8187
numpy/polynomial/_polytypes.pyi,sha256=e-uO5HmbYsWffZtOKCDgrxEqvUm-YKTqQKXj83m8j6s,22382
numpy/polynomial/chebyshev.py,sha256=T0vrDsOrO8Ntxbzf_-0dv_lPyF5c45OjDoVJDzeGBAI,62322
numpy/polynomial/chebyshev.pyi,sha256=jn21NMBsc4FYvC_5BM4kOfnYEaUSINdq3RyooS-5rjU,4787
numpy/polynomial/hermite.py,sha256=IguwJittKDh3y0rF1M9lLuIptFXgq-PhaHNTjfE3CnA,54603
numpy/polynomial/hermite.pyi,sha256=bNrlxTVHTskFUOKDbyrISXbOsmPxxhnAGmZmOF1mLpc,2463
numpy/polynomial/hermite_e.py,sha256=fhuui2jLc0I5WEEsRDcyw8FKSFxOl9jr8b4yRIxEZqQ,52305
numpy/polynomial/hermite_e.pyi,sha256=OyjRyzP7tz5sDP-90D4dpn82zJ4zPUCIzhpXaOCpkCY,2555
numpy/polynomial/laguerre.py,sha256=XJ5dNqWuZNhqwARb_QW4nfrRHyJv1JMCgsP2W4-KE9M,52474
numpy/polynomial/laguerre.pyi,sha256=_72JssagROc-vwt8W1i3aOo8s5l2v2G2NzMUm14vZnw,2191
numpy/polynomial/legendre.py,sha256=sMJTmGdewNhccrK9CyfNIIFRgzmY-AJHhgo6zxtGYvo,51129
numpy/polynomial/legendre.pyi,sha256=dPizRI4HLqAQ8Jms8Ta_HtsUyHV49fk3hFCZNOid1fo,2191
numpy/polynomial/polynomial.py,sha256=-IICosb2j8ClsIfXPDWgXqLx6WuhU6olocU4JkxN7kI,52196
numpy/polynomial/polynomial.pyi,sha256=A3oK3wKteiRkUcNEkzgvZQ11HIqloIRoxG2X9rPVZBE,2021
numpy/polynomial/polyutils.py,sha256=mQEa3oCz9X-d1HaNdXkpBJzXWGzgY42WDMjJOn988O8,22657
numpy/polynomial/polyutils.pyi,sha256=gnB7TQZclbMGneVVFE1z5LX5Qgs3GCidRTWL97rja-4,10235
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_chebyshev.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_classes.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite_e.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_laguerre.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_legendre.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_polynomial.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_polyutils.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_printing.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_symbol.cpython-313.pyc,,
numpy/polynomial/tests/test_chebyshev.py,sha256=gcK5jVv1vG3O-VVMkZKpmweR6_4HQAXtzvbJ_ib0-B8,20650
numpy/polynomial/tests/test_classes.py,sha256=nBsVHcubheo1s7t-jUXY984ptC2x-aWDPkWED1cUZt4,18552
numpy/polynomial/tests/test_hermite.py,sha256=sexvJUDmac1JKL8qOeQr70KJTD1KdoJ10LKosFfBqm0,18687
numpy/polynomial/tests/test_hermite_e.py,sha256=r3QQOUVoBBVgZzCjE3qzIl-wMcl_kI1Nuc-KGNy7rIw,19026
numpy/polynomial/tests/test_laguerre.py,sha256=8c2h7Lj3F2DtuVuOPlS8ZL-dq_IoFxPrzREbuI5iZqQ,17637
numpy/polynomial/tests/test_legendre.py,sha256=hMdOs_RzkGihUzg7gDmeM1FxkIT2UIgqkDWanucfMHg,18805
numpy/polynomial/tests/test_polynomial.py,sha256=Pi_X6ThfxgVbgzyAnu3FcyTIUvpL9ENxRSanyUjgon8,22911
numpy/polynomial/tests/test_polyutils.py,sha256=gO7B1oPBRRClF7WeXFsLjGwqUl9kjVIv7aAoHlhqVsk,3780
numpy/polynomial/tests/test_printing.py,sha256=qk76AKCvHHqbsDnHIVf5fxIEH9Va4U9jwJkJ1b67k1o,21403
numpy/polynomial/tests/test_symbol.py,sha256=ShBdNg9cvYy31fQnrn4gprZUSD0shz5r8zlG8CEq7gs,5375
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/LICENSE.md,sha256=EDFmtiuARDr7nrNIjgUuoGvgz_VmuQjxmeVh_eSa8Z8,3511
numpy/random/__init__.pxd,sha256=9JbnX540aJNSothGs-7e23ozhilG6U8tINOUEp08M_k,431
numpy/random/__init__.py,sha256=WFzntztUVNaiXCpQln8twyL8HSFNS7XAWJlJsQXgbqk,7480
numpy/random/__init__.pyi,sha256=5X5UqSDkeruZafGWv9EnYb0RrjRs49r-TlzV3PPQOjs,2109
numpy/random/__pycache__/__init__.cpython-313.pyc,,
numpy/random/__pycache__/_pickle.cpython-313.pyc,,
numpy/random/_bounded_integers.cpython-313-darwin.so,sha256=T6lrjECSd_ZxDkTsjBi6LMljtx15pj8uoGgENbH4nso,282088
numpy/random/_bounded_integers.pxd,sha256=SH_FwJDigFEInhdliSaNH2H2ZIZoX02xYhNQA81g2-g,1678
numpy/random/_bounded_integers.pyi,sha256=juqd9PbXs4yg45zMJ7BHAOPQjb7sgEbWE9InBtGZhfo,24
numpy/random/_common.cpython-313-darwin.so,sha256=8NC9gcLHUjzJ1nALfkp-PaNglVr0ApXz9hZiAGu5gIc,235536
numpy/random/_common.pxd,sha256=7kGArYkBcemrxJcSttwvtDGbimLszdQnZdNvPMgN5xQ,4982
numpy/random/_common.pyi,sha256=02dQDSAflunmZQFWThDLG3650py_DNqCmxjmkv5_XpA,421
numpy/random/_examples/cffi/__pycache__/extending.cpython-313.pyc,,
numpy/random/_examples/cffi/__pycache__/parse.cpython-313.pyc,,
numpy/random/_examples/cffi/extending.py,sha256=jpIL1njMhf0nehmlMHkgZkIxns2JC9GEDYgAChX87G8,884
numpy/random/_examples/cffi/parse.py,sha256=PK9vdUxwmvdnFvH3rOpgnnpISwnid7ri5XOmBrMWpJw,1750
numpy/random/_examples/cython/extending.pyx,sha256=ePnHDNfMQcTUzAqgFiEqrTFr9BoDmbqgjxzrDLvV8fE,2267
numpy/random/_examples/cython/extending_distributions.pyx,sha256=ahvbdSuRj35DKJRaNFP5JDuPqveBBp-M9mFfF3Wd_M4,3866
numpy/random/_examples/cython/meson.build,sha256=GxZZT_Lu3nZsgcqo_7sTR_IdMJaHA1fxyjwrQTcodPs,1694
numpy/random/_examples/numba/__pycache__/extending.cpython-313.pyc,,
numpy/random/_examples/numba/__pycache__/extending_distributions.cpython-313.pyc,,
numpy/random/_examples/numba/extending.py,sha256=Z7Z_Xp7HPE4K5BZ7AwpZ29qvuftFAkvhMtNX53tlMMw,1959
numpy/random/_examples/numba/extending_distributions.py,sha256=fdePXeUj46yXK0MK1cszxUHQiOTiNuNsrbZqPw4AdGs,2036
numpy/random/_generator.cpython-313-darwin.so,sha256=QSGNX4IJEh-n4AMA16Mto50fZDb25Udr0354cu1v1R8,790976
numpy/random/_generator.pyi,sha256=aFPqfOxIpOIOmdY1xBcUpllMCv20iTq4PN7Ad_gd7HY,24009
numpy/random/_mt19937.cpython-313-darwin.so,sha256=4pKaYYBF83swdihFBai5AIlIeDpdExPxxtgC7Byj6Gw,134000
numpy/random/_mt19937.pyi,sha256=ZjOCfOQb1KLDywy8ZHy8pQb1C-DZvStqYK3OOB6rETo,775
numpy/random/_pcg64.cpython-313-darwin.so,sha256=3L29ylaGJZNDIv1WSxxPQ77RJZbyO8ESsfSQdbN4_QA,134480
numpy/random/_pcg64.pyi,sha256=bIlGJyN2X3gtKEzh6qwDdyXX88al_2vVmCzGNpbNifs,1142
numpy/random/_philox.cpython-313-darwin.so,sha256=uCBTZB7mZUsyWIaeW5dgnj3r6MlQNH7S1QByaEyKXHI,116864
numpy/random/_philox.pyi,sha256=xFogUASfSHdviqexIf4bGgkzbryir7Tik7z0XQR9xx4,1005
numpy/random/_pickle.py,sha256=Lt47ma_vnnJHdnQlc5jZ_DqBHsdKi0QiUNaIkMf95qA,2742
numpy/random/_pickle.pyi,sha256=5obQY7CZRLMDjOgRtNgzV_Bg5O9E8DK_G74j7J7q6qo,1608
numpy/random/_sfc64.cpython-313-darwin.so,sha256=NbLzO5sBV6NP9ngOHjH7pj4ToxBI-kNFdNLmh4kYMrQ,97856
numpy/random/_sfc64.pyi,sha256=wRrbkEGLNhjXa7-LyGNtO5El9c8B_hNRQqF0Kmv6hQM,682
numpy/random/bit_generator.cpython-313-darwin.so,sha256=veBL-5iDk7_a7duIQbswZATrOmC1AUdth2ZinGsLVE8,211904
numpy/random/bit_generator.pxd,sha256=lArpIXSgTwVnJMYc4XX0NGxegXq3h_QsUDK6qeZKbNc,1007
numpy/random/bit_generator.pyi,sha256=tX5lVJDp6J5bNzflo-1rNylceD30oDBYtbiYVA1cWOY,3604
numpy/random/c_distributions.pxd,sha256=UCtqx0Nf-vHuJVaqPlLFURWnaI1vH-vJRE01BZDTL9o,6335
numpy/random/lib/libnpyrandom.a,sha256=W4FXDxEBUxH54Q81KKsvyULBlD5bULRcLFhf6FEy698,55288
numpy/random/mtrand.cpython-313-darwin.so,sha256=LT2wwc4gjV4m43YDSFhHd9NeiRFKkhJlh0O910IRZak,663408
numpy/random/mtrand.pyi,sha256=Ds2d-DloxUUE2wNNMA1w6oqqPsgBilkaRMCLioBTiJA,22687
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_direct.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_extending.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937_regressions.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_random.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_randomstate.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_randomstate_regression.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_seed_sequence.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_smoke.cpython-313.pyc,,
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__pycache__/__init__.cpython-313.pyc,,
numpy/random/tests/data/generator_pcg64_np121.pkl.gz,sha256=EfQ-X70KkHgBAFX2pIPcCUl4MNP1ZNROaXOU75vdiqM,203
numpy/random/tests/data/generator_pcg64_np126.pkl.gz,sha256=fN8deNVxX-HELA1eIZ32kdtYvc4hwKya6wv00GJeH0Y,208
numpy/random/tests/data/mt19937-testset-1.csv,sha256=Xkef402AVB-eZgYQkVtoxERHkxffCA9Jyt_oMbtJGwY,15844
numpy/random/tests/data/mt19937-testset-2.csv,sha256=nsBEQNnff-aFjHYK4thjvUK4xSXDSfv5aTbcE59pOkE,15825
numpy/random/tests/data/pcg64-testset-1.csv,sha256=xB00DpknGUTTCxDr9L6aNo9Hs-sfzEMbUSS4t11TTfE,23839
numpy/random/tests/data/pcg64-testset-2.csv,sha256=NTdzTKvG2U7_WyU_IoQUtMzU3kEvDH39CgnR6VzhTkw,23845
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=vNSUT-gXS_oEw_awR3O30ziVO4seNPUv1UIZ01SfVnI,23833
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=uylS8PU2AIKZ185OC04RBr_OePweGRtvn-dE4YN0yYA,23839
numpy/random/tests/data/philox-testset-1.csv,sha256=SedRaIy5zFadmk71nKrGxCFZ6BwKz8g1A9-OZp3IkkY,23852
numpy/random/tests/data/philox-testset-2.csv,sha256=dWECt-sbfvaSiK8-Ygp5AqyjoN5i26VEOrXqg01rk3g,23838
numpy/random/tests/data/sfc64-testset-1.csv,sha256=iHs6iX6KR8bxGwKk-3tedAdMPz6ZW8slDSUECkAqC8Q,23840
numpy/random/tests/data/sfc64-testset-2.csv,sha256=FIDIDFCaPZfWUSxsJMAe58hPNmMrU27kCd9FhCEYt_k,23833
numpy/random/tests/data/sfc64_np126.pkl.gz,sha256=MVa1ylFy7DUPgUBK-oIeKSdVl4UYEiN3AZ7G3sdzzaw,290
numpy/random/tests/test_direct.py,sha256=-ugW0cpuYhFSGVDtAbpEy_uFk-cG0JKFpPpQMDyFJh4,19919
numpy/random/tests/test_extending.py,sha256=8KgkOAbxrgU9_cj9Qm0F8r9qVEVy438Q-Usp7_HpSLQ,4532
numpy/random/tests/test_generator_mt19937.py,sha256=X0AEzi3xy6FzyTpTZNT_lXyXS_LWOWFYc9nZ6QtkILQ,117812
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=QZVFTSN9gnJXN-ye89JfUoov1Cu65r4e32FMmCYje5U,8107
numpy/random/tests/test_random.py,sha256=YSlHTwu6t7BAjDLZrBz4e8-ynSuV6eOHP9NwxDoZBvU,70298
numpy/random/tests/test_randomstate.py,sha256=WbZBpZplBlgmhWKXNsj7d0Zw0BHJ2nxEerMRnuwyYnE,85749
numpy/random/tests/test_randomstate_regression.py,sha256=1NgkJ60dVg8-UZ-ApepKlZGotqgenW_vZ3jqofMOSlw,8010
numpy/random/tests/test_regression.py,sha256=DqqLLE3_MW04ltPhSXy44oFx_daO9b4I7NgI-WoMc-s,5471
numpy/random/tests/test_seed_sequence.py,sha256=0lb4LRofbt_wHO-Cs_d1hwp1WcWjOmxH-OePkXST5bc,3310
numpy/random/tests/test_smoke.py,sha256=epkUF47HanaSZVz9NVUt6xUmKZhJNolPIB-z4MN67Qw,28141
numpy/rec/__init__.py,sha256=kNAYYoSAA0izpUVRb-18sJw-iKtFq2Rl2U5SOH3pHRM,83
numpy/rec/__init__.pyi,sha256=1ZL2SbvFSaoXwOK-378QQ0g0XldOjskx2E2uIerEGUI,347
numpy/rec/__pycache__/__init__.cpython-313.pyc,,
numpy/strings/__init__.py,sha256=o27wHW8jGaUfbDopSyEmYD6Rjeo33AzkGBBTgWrlGH4,83
numpy/strings/__init__.pyi,sha256=JP8YQR3xZ_mPMdQax7QSR2cZ-N-V7ZDqvOcWIIUP_54,1319
numpy/strings/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/__init__.py,sha256=Eqe-Ox-3JSqk6QRnnPPFLCW9Ikqv9OuJDhnm2uGM3zc,581
numpy/testing/__init__.pyi,sha256=1jr2Gj9BmCdtK4bqNGkwUAuqwC4n2JPOy6lqczK7xpA,2045
numpy/testing/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/__pycache__/overrides.cpython-313.pyc,,
numpy/testing/__pycache__/print_coercion_tables.cpython-313.pyc,,
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/_private/__pycache__/extbuild.cpython-313.pyc,,
numpy/testing/_private/__pycache__/utils.cpython-313.pyc,,
numpy/testing/_private/extbuild.py,sha256=Lg1sqA94Q74Ki5u-sx0PEj7urr3YP470-BCiyvJwExQ,7716
numpy/testing/_private/extbuild.pyi,sha256=aNH6UnAhh4Zny81W45GrAcScB12b6_84y8M0Vdtpm2I,626
numpy/testing/_private/utils.py,sha256=PZFbAxTSOPFQ_VXMaDCgPFBSEk2kcEGh8GRiBJy_yJg,95707
numpy/testing/_private/utils.pyi,sha256=9xlm7AQwi1yqOZN_t22jI_G9Ov-0tzX5H0ITHVz0UEE,12943
numpy/testing/overrides.py,sha256=B8Y8PlpvK71IcSuoubXWj4L5NVmLVSn7WMg1L7xZO8k,2134
numpy/testing/overrides.pyi,sha256=IQvQLxD-dHcbTQOZEO5bnCtCp8Uv3vj51dl0dZ0htjg,397
numpy/testing/print_coercion_tables.py,sha256=SboNmCLc5FyV-UR8gKjJc2PIojN1XQTvH0WzDq75M2M,6286
numpy/testing/print_coercion_tables.pyi,sha256=FRNibMYi0OyLIzKD4RUASZyhlsTY8elN0Q3jcBPEdgE,821
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/tests/__pycache__/test_utils.cpython-313.pyc,,
numpy/testing/tests/test_utils.py,sha256=yb2RpPDZvVagXiwQPFhV2IhwslZRkC-d-Vtb5wbJbbo,69575
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/tests/__pycache__/test__all__.cpython-313.pyc,,
numpy/tests/__pycache__/test_configtool.cpython-313.pyc,,
numpy/tests/__pycache__/test_ctypeslib.cpython-313.pyc,,
numpy/tests/__pycache__/test_lazyloading.cpython-313.pyc,,
numpy/tests/__pycache__/test_matlib.cpython-313.pyc,,
numpy/tests/__pycache__/test_numpy_config.cpython-313.pyc,,
numpy/tests/__pycache__/test_numpy_version.cpython-313.pyc,,
numpy/tests/__pycache__/test_public_api.cpython-313.pyc,,
numpy/tests/__pycache__/test_reloading.cpython-313.pyc,,
numpy/tests/__pycache__/test_scripts.cpython-313.pyc,,
numpy/tests/__pycache__/test_warnings.cpython-313.pyc,,
numpy/tests/test__all__.py,sha256=AXbT9VmRSTYq9beba4d1Eom_V9SVXXEtpkBdEW2XCqU,222
numpy/tests/test_configtool.py,sha256=vJswcByOu52gdUcO5NX_jxYRbCNNO80IihaBrHrP0AU,1739
numpy/tests/test_ctypeslib.py,sha256=RNTHi3cYOEPQno5zZQ_WyekW5E_0bVuwmn1AFgkDzY8,12375
numpy/tests/test_lazyloading.py,sha256=mMbie5VOu7S4uQBu66RNA2ipSsAY4C0lyoJXeHclAvk,1160
numpy/tests/test_matlib.py,sha256=RMduSGHBJuVFmk__Ug_hVeGD4-Y3f28G0tlDt8F7k7c,1854
numpy/tests/test_numpy_config.py,sha256=y4U3wnNW0Ags4W_ejhQ4CRCPnBc9p-4-B9OFDcLq9fg,1235
numpy/tests/test_numpy_version.py,sha256=6PIeISx9_Hglpxc3y6KugeAgB4eBkuZC-DFlXt4LocA,1744
numpy/tests/test_public_api.py,sha256=KqMtjIjq0_lp2ag4FTtulzypCqyZ43kuUlXgzd_Vkxc,27851
numpy/tests/test_reloading.py,sha256=T0NTsxAZFPY0LuAzbsy0wV_vSIZON7dwWSNjz_yzpDg,2367
numpy/tests/test_scripts.py,sha256=QpjsWc0vgi-IFLdMr81horvHAnjRI7RhYyO-edHxzcU,1665
numpy/tests/test_warnings.py,sha256=ynGuW4FOgjLcwdyi5AYCGCrmAu7jZlIQWPNK-0Yr800,2328
numpy/typing/__init__.py,sha256=FdaIH47j8uGEA5luTu-DnrOOTFw-3ne2JVHe-yn_7bA,6048
numpy/typing/__pycache__/__init__.cpython-313.pyc,,
numpy/typing/__pycache__/mypy_plugin.cpython-313.pyc,,
numpy/typing/mypy_plugin.py,sha256=1pcfLxJaYFdCPKQJVwHvdYbZSVdZ7RSIcg1QXHR7nqM,6541
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_isfile.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_runtime.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_typing.cpython-313.pyc,,
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=HJZH7HOYGyw5z1WAm6ujlXfdkOcqM2YVI92Zv-Wkaj0,3695
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=2917vcb59EaV406oGtA9lSQ8n_KDDyfvMrxj1Na6rPM,1200
numpy/typing/tests/data/fail/array_like.pyi,sha256=klBpaBcONODcPC1LztdVZ3UuIPwXN8kUK_e9s_QnZoo,496
numpy/typing/tests/data/fail/array_pad.pyi,sha256=mt-nhrs6f4YP7xgXizti7k_AwFAJ50yK97fMrMAAEds,137
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=i-0R4ExF_gtfXML5qirbsQLmDwJyg6_37HDYsk6g6tI,616
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=9Y8aD2lkDO7UcLo9ySR0pnVz0p2ofl2Lq4XTHgoMXxA,463
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=fnn3R8bMqRD3TIwhc793zgMYoXk3pG52JjkPynL52mw,404
numpy/typing/tests/data/fail/char.pyi,sha256=IrAk7rxT3ixBVwNWHzCWEsW9rF_oCXTOtwIG-q_Vx2A,2800
numpy/typing/tests/data/fail/chararray.pyi,sha256=aTqYSgwQUGUVUDkOly_Dy5SdOiHdKKTEbo6jD68KaH0,2356
numpy/typing/tests/data/fail/comparisons.pyi,sha256=zscovvsL89W8wrL43k4z8z1DLrjXmxBbu6lAOpiyhp0,750
numpy/typing/tests/data/fail/constants.pyi,sha256=OHaBJo6GYW94BlUWKQDat5jiit5ZAy_h-5bb1WUJnLU,78
numpy/typing/tests/data/fail/datasource.pyi,sha256=7om31_WCptsSn_z7E5p-pKFlswZItyZm9GQ-L5fXWqM,419
numpy/typing/tests/data/fail/dtype.pyi,sha256=crqAVZmBYLYV9N-ihiOnKCY1QK4iTxX7J4Tviac2Vq4,305
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=4QWkwE4sr5bKz5-OCjPzeUcr4V-wpaMsqee2PXDwyjw,458
numpy/typing/tests/data/fail/flatiter.pyi,sha256=3WblzrQewUBZo1mjTRWzwda_rQ0HSVamtz2XwH2CgCc,715
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=eWCbs1dcoJraAA3b5qME09sWWvsSdlDO912_OwQ_M7k,5685
numpy/typing/tests/data/fail/histograms.pyi,sha256=wgI2CG-P0jbDw4KohR_nbJxqa34PT1e6nmnLi9KbPQM,376
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=RNZLHeMOpSX594Eem4WyJrM_QouqndGRVj2YQakJN-E,517
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=JdvdZlgNUNzlOuY74T6Lt_hNOpveU6U1jhFGB9Iu6ZA,2817
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=Y3jlwigvtr5tFEHvr3SgguMVsYZe8cvsdgKcavgfucs,937
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=6Oc_wYI0mv0l74p4pEiVtKjkWFNg4WmXjGW7_2zEKS4,98
numpy/typing/tests/data/fail/lib_version.pyi,sha256=BvABs2aeC6ZHUGkrsowu80Ks22pbxUMwSPJ8c5i7H14,154
numpy/typing/tests/data/fail/linalg.pyi,sha256=h9bcCeP0ARGONB3iYGkdX-BFPsNI-pZq3C-nfKgbbBU,1381
numpy/typing/tests/data/fail/ma.pyi,sha256=inPaC4jP7hGPqQJn-rBspeJZnxJz7m1nVDYQxuMI8SE,6364
numpy/typing/tests/data/fail/memmap.pyi,sha256=uXPLcVx726Bbw93E5kdIc7K0ssvLIZoJfNTULMtAa_8,169
numpy/typing/tests/data/fail/modules.pyi,sha256=mEBLIY6vZAPIf2BuyJcMAR-FarSkT55FRlusrsR0qCo,603
numpy/typing/tests/data/fail/multiarray.pyi,sha256=lSV5JiLNz-CxHUlNbF1Bq3x7mOftfr1kiiG2DgtXilE,1656
numpy/typing/tests/data/fail/ndarray.pyi,sha256=65IDiOprlv-sg375SBmmh6_hYOzlucTVLe42GymniGM,381
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=hSdxKyxweyxAH32DGa_ZnZIXqPNh6CafBK90rjbi8cs,1061
numpy/typing/tests/data/fail/nditer.pyi,sha256=nRbQ66HcoKXDKIacbWD9pTq-523GJOqxzJ3r278lDsc,319
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=jGjoQhCLr8dbTCPvWkilJKAW0RRMbrY-iEHf24Happo,463
numpy/typing/tests/data/fail/npyio.pyi,sha256=vPYmFaPCFbr5V2AC3074w8hTCBUYxpSF4fi1sbbfopw,646
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=NJxviXTJIaDoz7q56dBrHCBNNG-doTu-oIryzwURxHQ,124
numpy/typing/tests/data/fail/random.pyi,sha256=IvKXQuxZhuK6M0u2x3Y4PhXvLoC8OFnUdoeneaqDiIE,2903
numpy/typing/tests/data/fail/rec.pyi,sha256=eeFXVvVg4DherRMA1T8KERtTiRN1ZIbarw4Yokb8WrU,741
numpy/typing/tests/data/fail/scalars.pyi,sha256=EQy8ovBZX49a7AgtRyI8uHgauQoVzAmjE3NALe97tEw,2849
numpy/typing/tests/data/fail/shape.pyi,sha256=VNucLx9ittp1a0AOyVPd6XKfERm0kq_ND1lOr-LXQ_s,131
numpy/typing/tests/data/fail/shape_base.pyi,sha256=8366-8mCNii1D0W6YrOhCNxo8rrfqQThO-dIVWNRHvA,157
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=g7-DY8Zc8pzTDyOBA-8t6yIFj1FZI9XpvVdbybQN2i0,330
numpy/typing/tests/data/fail/strings.pyi,sha256=wX9ROrRNhpH9g_ewNGjWuTKU-He4xaNxrtz2Dm3iPo8,2333
numpy/typing/tests/data/fail/testing.pyi,sha256=m8d2OZZ1DtsHfmnTwvdMRETUfo0lwRzaOXjuyNi08PQ,1399
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=ROt5iqOp9ENbXlMEG8dzUZxHD3N4lwcbyCffuJ4BLZE,936
numpy/typing/tests/data/fail/type_check.pyi,sha256=hRXyE4Ywx6zjtSgiHwKRs4k47M4hnPjj7yjVhi91IaU,397
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=v5rd68Y2TzLplIOaOXM4h66HqSv8XbapR0b3xaoUOdQ,589
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=ejCb6kb7mmxPH0QrDsYfdFSLLPFKx0IZ9xSLs3YXOzg,649
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=XBoxO597ponBkFcCfwCS3s-jKfcnDzC_K5n2uBPrD6E,505
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=SoFIznFd_xDifIsS0pv0aqS2BvhZaT6xsOA0zJrRJkA,200
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=n1nzRzRa_oKDdNExxB0qRIQr8MeDIosbLU6Vpgi6ZYo,322
numpy/typing/tests/data/mypy.ini,sha256=rfUCMP01SsfRLJ-MRGEicI9XW-HJDoTJ_ncaACuKJ0s,245
numpy/typing/tests/data/pass/__pycache__/arithmetic.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_constructors.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_like.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayprint.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayterator.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/bitwise_ops.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/comparisons.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/dtype.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/flatiter.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/index_tricks.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_user_array.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_utils.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_version.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/literal.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ma.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/mod.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/modules.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/multiarray.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_conversion.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_misc.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_shape_manipulation.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/nditer.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/numeric.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/numerictypes.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/random.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/recfunctions.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/scalars.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/shape.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple_py3.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunc_config.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunclike.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufuncs.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/warnings_and_errors.cpython-313.pyc,,
numpy/typing/tests/data/pass/arithmetic.py,sha256=t4UK-TROh0uYPlUNn5CZHdTysECmDZa04uUOCZO58cY,7762
numpy/typing/tests/data/pass/array_constructors.py,sha256=rfJ8SRB4raElxRjsHBCsZIkZAfqZMie0VE8sSKMgkHg,2447
numpy/typing/tests/data/pass/array_like.py,sha256=-wTiw2o_rLw1aeT7FSh60RKguhvxKyr_Vv5XNXTYeS4,1032
numpy/typing/tests/data/pass/arrayprint.py,sha256=y_KkuLz1uM7pv53qfq7GQOuud4LoXE3apK1wtARdVyM,766
numpy/typing/tests/data/pass/arrayterator.py,sha256=FqcpKdUQBQ0FazHFxr9MsLEZG-jnJVGKWZX2owRr4DQ,393
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=FmEs_sKaU9ox-5f0NU3_TRIv0XxLQVEZ8rou9VNehb4,964
numpy/typing/tests/data/pass/comparisons.py,sha256=5aGrNl3D7Yd1m9WVkHrjJtqi7SricTxrEMtmIV9x0aE,3298
numpy/typing/tests/data/pass/dtype.py,sha256=YDuYAb0oKoJc9eOnKJuoPfLbIKOgEdE04_CYxRS4U5I,1070
numpy/typing/tests/data/pass/einsumfunc.py,sha256=eXj5L5MWPtQHgrHPsJ36qqrmBHqct9UoujjJCvHnF1k,1370
numpy/typing/tests/data/pass/flatiter.py,sha256=tpKL_EAjkJoCZ5C0iuIX0dNCwQ9wUq1XlBMP-n2rjM4,203
numpy/typing/tests/data/pass/fromnumeric.py,sha256=d_hVLyrVDFPVx33aqLIyAGYYQ8XAJFIzrAsE8QCoof4,3991
numpy/typing/tests/data/pass/index_tricks.py,sha256=dmonWJMUKsXg23zD_mibEEtd4b5ys-sEfT9Fnnq08x8,1402
numpy/typing/tests/data/pass/lib_user_array.py,sha256=Za_n84msWtV8dqQZhMhvh7lzu5WZvO8ixTPkEqO2Hms,590
numpy/typing/tests/data/pass/lib_utils.py,sha256=bj1sEA4gsmezqbYdqKnVtKzY_fb64w7PEoZwNvaaUdA,317
numpy/typing/tests/data/pass/lib_version.py,sha256=HnuGOx7tQA_bcxFIJ3dRoMAR0fockxg4lGqQ4g7LGIw,299
numpy/typing/tests/data/pass/literal.py,sha256=HSG-2Gf7J5ax3mjTOeh0pAYUrVOqboTkrt2m6ssfqVY,1508
numpy/typing/tests/data/pass/ma.py,sha256=ZIi85AwntBX7M1LIvl4yEGixAauHAS2GINBR42Ri4Hw,3362
numpy/typing/tests/data/pass/mod.py,sha256=owFL1fys3LPTWpAlsjS-IzW4sSu98ncp2BnsIetLSrA,1576
numpy/typing/tests/data/pass/modules.py,sha256=g9PhyLO6rflYHZtmryx1VWTubphN4TAPUSfoiYriTqE,625
numpy/typing/tests/data/pass/multiarray.py,sha256=MxHax6l94yqlTVZleAqG77ILEbW6wU5osPcHzxJ85ns,1331
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=d7cFNUrofdLXh9T_9RG3Esz1XOihWWQNlz5Lb0yt6dM,1525
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=wBbQDHcpiIlMl-z5ToVOrFpoxrqXQMBq1dFSWfwGJNE,3699
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=37eYwMNqMLwanIW9-63hrokacnSz2K_qtPUlkdpsTjo,640
numpy/typing/tests/data/pass/nditer.py,sha256=nYO45Lw3ZNbQq75Vht86zzLZ4cWzP3ml0rxDPlYt8_8,63
numpy/typing/tests/data/pass/numeric.py,sha256=pOwxnmZmdCtDKh9ih0h5GFIUPJwsi97NBs1y5ZAGyUM,1622
numpy/typing/tests/data/pass/numerictypes.py,sha256=6x6eN9-5NsSQUSc6rf3fYieS2poYEY0t_ujbwgF9S5Q,331
numpy/typing/tests/data/pass/random.py,sha256=UJF6epKYGfGq9QlrR9YuA7EK_mI8AQ2osdA4Uhsh1ms,61824
numpy/typing/tests/data/pass/recfunctions.py,sha256=GwDirrHsL3upfIsAEZakPt95-RLY7BpXqU_KXxi4HhQ,5003
numpy/typing/tests/data/pass/scalars.py,sha256=pzV3Y20dd6xB9NRsJ0YSdkcvI5XcD8cEWtEo1KTL1SU,3724
numpy/typing/tests/data/pass/shape.py,sha256=L2iugxTnbm8kmBpaJVYpURKJEAnI7TH2KtuYeqNR9co,445
numpy/typing/tests/data/pass/simple.py,sha256=lPj620zkTA8Sg893eu2mGuj-Xq2BGZ_1dcmfsVDkz8g,2751
numpy/typing/tests/data/pass/simple_py3.py,sha256=HuLrc5aphThQkLjU2_19KgGFaXwKOfSzXe0p2xMm8ZI,96
numpy/typing/tests/data/pass/ufunc_config.py,sha256=uzXOhCl9N4LPV9hV2Iqg_skgkKMbBPBF0GXPU9EMeuE,1205
numpy/typing/tests/data/pass/ufunclike.py,sha256=U4Aay11VALvm22bWEX0eDWuN5qxJlg_hH5IpOL62M3I,1125
numpy/typing/tests/data/pass/ufuncs.py,sha256=1Rem_geEm4qyD3XaRA1NAPKwr3YjRq68zbIlC_Xhi9M,422
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=ETLZkDTGpZspvwjVYAZlnA1gH4PJ4bSY5PkWyxTjusU,161
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=phWM8Fz30fe--KkKI8S9voIbDNHbxIKSzLwRWwvJ7yU,27424
numpy/typing/tests/data/reveal/array_api_info.pyi,sha256=oWKW0yGS9xKcLZnH2QeeixMBcI74dNIcwZr0bwGmDVM,3017
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=68eDzCyTkluwvczSdiiclfn_1yx5aidRosGmRuE94SA,12932
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=Dg5ss1cDS_QiNT4YEheHXMa2beM4qBTUb1mq-REkh6A,653
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=iUHzZaUrYFGC9QBCxhiEAIJODeqGwG7VCv875il-9gY,777
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=Hhe49rLgj0P8SXElncNvLeCv1OqdI-iryB_673w7vL4,4411
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=QPRyZzHFmti4HlrJ315dgzBjaet8LqM9il-8uc9e2P8,1039
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=TjW0vMyXqUy-WoEIMA3AMN_u4IGw5RosOWK_qHMNjes,4911
numpy/typing/tests/data/reveal/char.pyi,sha256=u0_0Eycmn5YxxKLOcjRbJkLihXXXSGQ3E6PnX8uHGew,10772
numpy/typing/tests/data/reveal/chararray.pyi,sha256=4oqRNZt7jIdfbNVgcsWPDVVFrrEYhqjAExaNzPya_lY,5199
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=mXRfm3ZUsk8YbSPg9ugPSWLGRwzUVy4BEVN7q4K56tc,7195
numpy/typing/tests/data/reveal/constants.pyi,sha256=AazwlvF--Te1dt35f8lkDLNuo3jQXqmGvddDQ37jAE0,333
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=U9ZO5GnGHxVyv-OWRYWHSXctH7LGHPWDdyNVl_saQEQ,4134
numpy/typing/tests/data/reveal/datasource.pyi,sha256=B9nCoOPE4fJvBIeInAgUCg5pIsr8IYOu_iToqt6n-Nc,583
numpy/typing/tests/data/reveal/dtype.pyi,sha256=IdxNE3NIE0YKpVw4yI9lS-wWPmeFyfGCW2V0oyor4zk,5080
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=qPYk5W3lardDdgsQIGyu356iIGDnb0P38UKQDXWQlrk,1926
numpy/typing/tests/data/reveal/emath.pyi,sha256=fcf0-GftYRByfJFuZC-MvzHlQU4A-f9-kPnxzQt48E0,2125
numpy/typing/tests/data/reveal/fft.pyi,sha256=uZOJ0ljmmnejfPEwMsfUGDb52NOuTh7Npl7ONwx-Y2k,1601
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=ZxgdgbRWYXlyxlPOXJzZSHvALqGsK3aV4lf9RePghdA,1347
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=xweKmm6uKVgJF4-AwtM6hGEI_YHosu-8jXnd8yjSfJ4,15066
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=mH0kk94VBu-O5ZzA1nki80jttDK_EBGOsLQOZo3Rq18,1547
numpy/typing/tests/data/reveal/histograms.pyi,sha256=Mr7P7JYMWF9jM6w5othyzh8CN3ygd2A-WRoB4jImnzk,1257
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=4dvG8RXY5ktKXo1uC_pfPHXBDd7tatTbjCs8xr8M2os,3241
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=LMCyduuUjX1E7ruBI-B_cEJQ_rUt9ZO21ck22_OLa_c,10112
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=CrG0zxbY-HddD7D93q5Cow6c_3mx3nVb1ZCcAq5mC4U,5660
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=oQCay2NF8pYHD5jNgRZKNjn8uJW4TJqUPIlytOwDSi0,436
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=y4ZJSLEeS273Zd6fqaE2XNdczTS0-cwIJ2Yn_4Otm44,572
numpy/typing/tests/data/reveal/linalg.pyi,sha256=w8RdvwTSt40PMQDvlt_tnky4Cu9LnTUXAmdFhZORPpc,5933
numpy/typing/tests/data/reveal/ma.pyi,sha256=Ho1-A7a0zrGnQob-Zo7qphG0dfNzl74xMoh5XPGcbr0,16298
numpy/typing/tests/data/reveal/matrix.pyi,sha256=ntknd4qkGbaBMMzPlkTeahyg_H8_TDBJQDbd36a_QfY,3040
numpy/typing/tests/data/reveal/memmap.pyi,sha256=OCcEhR5mvvXk4UhF6lRqmkxU2NcAqJ4nqAuBpcroQ1g,719
numpy/typing/tests/data/reveal/mod.pyi,sha256=9nJnn1rA_4mbk2JSYyDmQ5pMWWQ9vPDDzWqijlFAG4I,7599
numpy/typing/tests/data/reveal/modules.pyi,sha256=_Gvxgql5KbJFL1Mj5gFAphzyGC44AkuNZLnYkv-3LRA,1858
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=oz81sV4JUBbd6memodStUpT11TARzqRXWUs4H0cU-YA,7779
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=9OqWKUGRGCIt-mywzDmZExTOsM7l3JGw0YAPB9rs_8k,687
numpy/typing/tests/data/reveal/ndarray_assignability.pyi,sha256=KOl5ActvtUx6h1oTQT3c0EiU5eCDbMD1okQVfxpc4j0,2668
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=SAI9kxMNl66L8n7kO3jn7-EL_3Ygn46behqD_dVa5Hw,3309
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=8jwi9O-iGcojU0xSF_GUYMFRpkRdol5hQza0hkziNXc,8663
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=z8SRTWdl6fSj_ENNF-M5jZnujUl1180WaFMAanXqCVw,1394
numpy/typing/tests/data/reveal/nditer.pyi,sha256=yih7UE0OynR7GuVCGgwhzjTjARwOXikDe6Dr4ymRC2g,1898
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=Z2vwweUjoqxR0zUUldOUXsg6mkDfDP1BMyFV2hje5Z8,612
numpy/typing/tests/data/reveal/npyio.pyi,sha256=p6jJFmcwXuQhshYC70zhg_itI1kLiDu9saUCNwYpFNo,3493
numpy/typing/tests/data/reveal/numeric.pyi,sha256=0hvPN803QJoO38lYY68of7M-1KGXqdgHy9RdqcHwO-M,5869
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=4lnZQTgVtig8UuDwuETyQ6jRFxsYv6tnni2ZJaDyMM0,1331
numpy/typing/tests/data/reveal/polynomial_polybase.pyi,sha256=V7ulOvXuAcduWTD_7Jg1yPCLvROq8E-10GobfNlKXD8,7925
numpy/typing/tests/data/reveal/polynomial_polyutils.pyi,sha256=I_4waxJEeUsp5pjnbBN55kqZ2kycK8akD_XvhsgsCGY,10642
numpy/typing/tests/data/reveal/polynomial_series.pyi,sha256=YowKiIaDd2Je0PjEmXDINUXe4il0r4KDkpzDbYpwG38,6853
numpy/typing/tests/data/reveal/random.pyi,sha256=xXJobSp5nVBelmrBO_OTvV8XQnbnZjbAyJfrRwlJshg,104296
numpy/typing/tests/data/reveal/rec.pyi,sha256=E8lxkOQ4qSwwX20Y4d438s5g-kTnNARsZc4f-Y8OhZo,3378
numpy/typing/tests/data/reveal/scalars.pyi,sha256=5s5Xm1HoA6bwwqK4gfEWqoNk45dAQvxAZLZc2zUhe3A,6378
numpy/typing/tests/data/reveal/shape.pyi,sha256=ZT6e5LW4nU90tA-Av5NLiyoaPW9NIX_XkWJ-LOOzh84,262
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=xbnt0jps1djVxVMn4Lj8bxGl-mGvbhqSKFVWYcFApLg,2006
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=Cm9P_F7promu0zGZmo957SOFCZ6Np8wSv5ecR_hB668,1315
numpy/typing/tests/data/reveal/strings.pyi,sha256=WvSd8xHIdxQdah3Q0ZJUva79jfVngB3UD9yb6awDW8w,9547
numpy/typing/tests/data/reveal/testing.pyi,sha256=vP3uEWEdFHrfv_Q4OaJ0Oo5gUqUxkkIRVjvJMsqiHs8,8443
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=TiBbWXI0xRCgk0bE-Bd4ZryWaLeJIQ5I-6KBjIVoMuE,4237
numpy/typing/tests/data/reveal/type_check.pyi,sha256=W7rJUEf_iwI0D1FIVjhCEfzIjw_T04qcBYFxuPwnXAo,2392
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=XoD9fxaMVCGgyMncWKIJssFBO0SmndHsDs0hDXS04A8,1162
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=0jwIYSgXn0usVGkzyZz0ttO5tSYfWMYu_U2ByqrzuRQ,1183
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=2IYvfPlLCuqgoyNKzbcv3mr-Dva2cyUSWtBWuM77sDk,4789
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=5qqRFzPOon1GhU_i5CHDxQLPKVcO2EMhbc851V8Gusc,449
numpy/typing/tests/test_isfile.py,sha256=yaRIX3JLmwY1cgD-xxKvJjMVVBRmv9QNSXx9kQSoVAc,878
numpy/typing/tests/test_runtime.py,sha256=YHS0Hgv1v3cip7C14UcsJWLGI37m18MqXrwLmb88Ctc,2919
numpy/typing/tests/test_typing.py,sha256=VERPf6NJ6gRLoKk0ki-s1wvDS4E--InjNUaj63_Q-00,6289
numpy/version.py,sha256=fJGWkFzsiRYbzitPSWyuaTKhd7M21u_HRTuyqnR21rI,293
numpy/version.pyi,sha256=x3oCrDqM_gQhitdDgfgMhJ-UPabIXk5etqBq8HUwUok,358
