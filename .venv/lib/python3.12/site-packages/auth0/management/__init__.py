from ..utils import is_async_available
from .actions import Actions
from .attack_protection import AttackProtection
from .blacklists import Blacklists
from .branding import Branding
from .client_credentials import ClientCredentials
from .client_grants import ClientGrants
from .clients import Clients
from .connections import Connections
from .custom_domains import CustomDomains
from .device_credentials import DeviceCredentials
from .email_templates import EmailTemplates
from .emails import Emails
from .grants import Grants
from .guardian import Guardian
from .hooks import Hooks
from .jobs import Jobs
from .log_streams import LogStreams
from .logs import Logs
from .organizations import Organizations
from .resource_servers import ResourceServers
from .roles import Roles
from .rules import Rules
from .rules_configs import RulesConfigs
from .self_service_profiles import SelfServiceProfiles
from .stats import Stats
from .tenants import Tenants
from .tickets import Tickets
from .user_blocks import UserBlocks
from .users import Users
from .users_by_email import UsersByEmail

if is_async_available():
    from .async_auth0 import AsyncAuth0 as Auth0
else:  # pragma: no cover
    from .auth0 import Auth0  # type: ignore[assignment]

__all__ = (
    "Auth0",
    "Actions",
    "AttackProtection",
    "Blacklists",
    "Branding",
    "ClientCredentials",
    "ClientGrants",
    "Clients",
    "Connections",
    "CustomDomains",
    "DeviceCredentials",
    "EmailTemplates",
    "Emails",
    "Grants",
    "Guardian",
    "Hooks",
    "Jobs",
    "LogStreams",
    "Logs",
    "Organizations",
    "ResourceServers",
    "Roles",
    "RulesConfigs",
    "Rules",
    "SelfServiceProfiles",
    "Stats",
    "Tenants",
    "Tickets",
    "UserBlocks",
    "UsersByEmail",
    "Users",
)
