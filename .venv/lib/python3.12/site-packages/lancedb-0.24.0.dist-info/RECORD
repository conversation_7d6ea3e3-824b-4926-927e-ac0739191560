lancedb-0.24.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
lancedb-0.24.0.dist-info/METADATA,sha256=BVerxT-Usc3qfr7V3VIuWVbRU1kaMqo6izLj38EqB-U,4402
lancedb-0.24.0.dist-info/RECORD,,
lancedb-0.24.0.dist-info/WHEEL,sha256=ShNW9bA0SsmbQCHahW07Jn2BXKPw0MrZOIA0fk7HI7M,104
lancedb-0.24.0.dist-info/licenses/LICENSE,sha256=WNHhf_5RCaeuKWyq_K39vmp9F28LxKsB4SpomwSZ2L0,11357
lancedb/__init__.py,sha256=RywvW7TbBeHO34HRSbDX_ZUfQpIfhCO1TOaRzPqUKsI,8112
lancedb/__pycache__/__init__.cpython-312.pyc,,
lancedb/__pycache__/arrow.cpython-312.pyc,,
lancedb/__pycache__/background_loop.cpython-312.pyc,,
lancedb/__pycache__/common.cpython-312.pyc,,
lancedb/__pycache__/conftest.cpython-312.pyc,,
lancedb/__pycache__/context.cpython-312.pyc,,
lancedb/__pycache__/db.cpython-312.pyc,,
lancedb/__pycache__/dependencies.cpython-312.pyc,,
lancedb/__pycache__/exceptions.cpython-312.pyc,,
lancedb/__pycache__/fts.cpython-312.pyc,,
lancedb/__pycache__/index.cpython-312.pyc,,
lancedb/__pycache__/merge.cpython-312.pyc,,
lancedb/__pycache__/pydantic.cpython-312.pyc,,
lancedb/__pycache__/query.cpython-312.pyc,,
lancedb/__pycache__/schema.cpython-312.pyc,,
lancedb/__pycache__/table.cpython-312.pyc,,
lancedb/__pycache__/types.cpython-312.pyc,,
lancedb/__pycache__/util.cpython-312.pyc,,
lancedb/_lancedb.abi3.so,sha256=nZCIoHlMa3do7B_vt8TgLvfdXQFZ1Gco4Yl9S7AGHk8,81899072
lancedb/_lancedb.pyi,sha256=dsaKK0glOpAcD6geUyscGRjLw7TVU-S_xTjLFUgECj8,7832
lancedb/arrow.py,sha256=y5_agsRsITJTpgp8m7iCQ_HE9p2fbD0zMNU7Ipk9cSc,2420
lancedb/background_loop.py,sha256=rpTiv5Mbkbar-ThfCfth9bTf_G29fq4PvpqTnTrulEo,700
lancedb/common.py,sha256=j5bt9nKEtyI7Z4KJC9KESykPQHFDQxXfSJHwkTYnObQ,4323
lancedb/conftest.py,sha256=hd7nnA-CsBCcODR6scRRPyN-0CqceL9AV6f8gvlp4UA,2418
lancedb/context.py,sha256=NKAAJiPRHr3xF1P17xhlhFlxExOb_iigp-I1P6FsFtE,8456
lancedb/db.py,sha256=K1RfCGWtD2jF0UzNDSgG9_BUjQJlJ4ld9-DMLMlKMrM,30205
lancedb/dependencies.py,sha256=NWstIeE17nQcnCF8JzGHNSIZFFxd3MH4UKIDRYG1BiY,8412
lancedb/embeddings/__init__.py,sha256=wofVvUgqJYpAmbcJVmkNFDzcxEFFt7DYEy3Kq2wnM30,917
lancedb/embeddings/__pycache__/__init__.cpython-312.pyc,,
lancedb/embeddings/__pycache__/base.cpython-312.pyc,,
lancedb/embeddings/__pycache__/bedrock.cpython-312.pyc,,
lancedb/embeddings/__pycache__/cohere.cpython-312.pyc,,
lancedb/embeddings/__pycache__/colpali.cpython-312.pyc,,
lancedb/embeddings/__pycache__/gemini_text.cpython-312.pyc,,
lancedb/embeddings/__pycache__/gte.cpython-312.pyc,,
lancedb/embeddings/__pycache__/gte_mlx_model.cpython-312.pyc,,
lancedb/embeddings/__pycache__/imagebind.cpython-312.pyc,,
lancedb/embeddings/__pycache__/instructor.cpython-312.pyc,,
lancedb/embeddings/__pycache__/jinaai.cpython-312.pyc,,
lancedb/embeddings/__pycache__/ollama.cpython-312.pyc,,
lancedb/embeddings/__pycache__/open_clip.cpython-312.pyc,,
lancedb/embeddings/__pycache__/openai.cpython-312.pyc,,
lancedb/embeddings/__pycache__/registry.cpython-312.pyc,,
lancedb/embeddings/__pycache__/sentence_transformers.cpython-312.pyc,,
lancedb/embeddings/__pycache__/transformers.cpython-312.pyc,,
lancedb/embeddings/__pycache__/utils.cpython-312.pyc,,
lancedb/embeddings/__pycache__/voyageai.cpython-312.pyc,,
lancedb/embeddings/__pycache__/watsonx.cpython-312.pyc,,
lancedb/embeddings/base.py,sha256=cuo_b4in1FFtXRReOqtgrZQXhyL1mq01zHOfC7UiYdo,7809
lancedb/embeddings/bedrock.py,sha256=nJ56tOzho4sSOB3lh346Ohsm0yjCmMaydD3LTT74OFM,7723
lancedb/embeddings/cohere.py,sha256=ZjCdxLbIhNeFoJ5Z2wrXxgjOl7ZNliyCWJb8_IHdMAo,4823
lancedb/embeddings/colpali.py,sha256=HECIkY8ncddl_EsFWxFbpKWVls47G8i7BqsM6NFEF-E,9434
lancedb/embeddings/gemini_text.py,sha256=dpv-f_24vActzVgQXwuZ6TQGrxqbZk0cPkP4H9L2Fes,5025
lancedb/embeddings/gte.py,sha256=prruKw_KB-iewju5Z1uUSTE8sizRpRjzEP1fm_jkNd8,3763
lancedb/embeddings/gte_mlx_model.py,sha256=aGIaReFsS9U5D-UqsUPOGBwTMUlZlr7F_CQSfwpDWSM,5201
lancedb/embeddings/imagebind.py,sha256=fs2ICwAf52OXj2hdprxOoyaeZCsTJMpVpejLMNRmIq0,5896
lancedb/embeddings/instructor.py,sha256=nx2fPaqpdbRVO7HXOxtZUlDZW7zfWLcZY14mbjnjFBU,5577
lancedb/embeddings/jinaai.py,sha256=w4myOSmlrRVtLOqq35MIxnAGxcDZWte0qMx5mtCDQjY,8138
lancedb/embeddings/ollama.py,sha256=d5QLe_fkq-h_T3Gw-nWqDiStbde-rcVB2bojWKQR1fE,1952
lancedb/embeddings/open_clip.py,sha256=xJcvdZIf8cCNbmUw4EeSHu2hWP39Cempc4fu6H7PHnA,6139
lancedb/embeddings/openai.py,sha256=ojYP1gt41nMOnvATHt4LdX7tBcuoTFDP9mP62aeNLxQ,3982
lancedb/embeddings/registry.py,sha256=WHz39sncPd8NjvwwC61EJTF86Muyazm9ibiC3jc6Of4,7090
lancedb/embeddings/sentence_transformers.py,sha256=qVJvgqJI9VRm19fI6AW2k1QW1zYqmlU9Y7M1ZmRFiCo,2631
lancedb/embeddings/transformers.py,sha256=Q9EaFbEYnpWPjrKuiL48yDCuFLGourmC87vVYJGjCk0,3778
lancedb/embeddings/utils.py,sha256=jhorSwZzQG3v6hNdHURkSQhmEx2jkeMamjlwFEtbqTU,8003
lancedb/embeddings/voyageai.py,sha256=LGbEnjZZKloUMpMrQ91s4oczZ_zmODVpcGUuSH4Lj-A,7930
lancedb/embeddings/watsonx.py,sha256=UZwT3b2UiHkyRJVrrNbV78FlF58GsQ8p-r5SCtU27ZY,3171
lancedb/exceptions.py,sha256=dnCoiwMTIcQyAqZZlCPkc7pVNUTxnEPwc2vj5BzCbBA,594
lancedb/fts.py,sha256=5Irvgtz7kE7BVtSpmkAPnh68kifhEzb-ZOUmte8vttk,5664
lancedb/index.py,sha256=5uj3EQ9Qy1lvhzMoMXvrI2lEPMor7jo7ZtalpByJJkE,23868
lancedb/integrations/__init__.py,sha256=IjusuNT4Y3Q_Nd5E9W2l1hdlghWCflVQVEGTYDzi8WU,94
lancedb/integrations/__pycache__/__init__.cpython-312.pyc,,
lancedb/integrations/__pycache__/pyarrow.cpython-312.pyc,,
lancedb/integrations/pyarrow.py,sha256=9uLrGwZU51WsTEEj1HueIRR9byn3VniTKkd5Nj1tUqg,7378
lancedb/merge.py,sha256=x4KDsExQUG6HvQ1goyMW9r-ewpSKRKdzKYnI2lzFt1M,4638
lancedb/pydantic.py,sha256=tHuMrVYYvUdV9EE-dEFUHbuAR-vkbWUCXnYPOc2sHC4,15565
lancedb/query.py,sha256=lVl7_qC2G_G1fgCEWjqZLo0B5RzsSyXIWVf9Y84HUKs,112273
lancedb/remote/__init__.py,sha256=91wUjQRManhksknxZTayA5w0phjAMck3SVhjAgk3dhQ,4804
lancedb/remote/__pycache__/__init__.cpython-312.pyc,,
lancedb/remote/__pycache__/db.cpython-312.pyc,,
lancedb/remote/__pycache__/errors.cpython-312.pyc,,
lancedb/remote/__pycache__/table.cpython-312.pyc,,
lancedb/remote/db.py,sha256=MP5vOrA2XQU8Z4Np0lpeey3A8DLCT6TPBXvcnFbDeJ0,9759
lancedb/remote/errors.py,sha256=FtgfSygD2qcHVf2C3c3zHSi9BWb6hWK9Qk6XdGWWu-I,3368
lancedb/remote/table.py,sha256=s0XaN5_PJ2pJ4rJk5Au68NfGzdXoQrKsLy4MDaD9sT4,21581
lancedb/rerankers/__init__.py,sha256=b2ytNJ3HnYn2Iq6ufTlNW2ABaiiQgc41f2If-UnFWn0,732
lancedb/rerankers/__pycache__/__init__.cpython-312.pyc,,
lancedb/rerankers/__pycache__/answerdotai.cpython-312.pyc,,
lancedb/rerankers/__pycache__/base.cpython-312.pyc,,
lancedb/rerankers/__pycache__/cohere.cpython-312.pyc,,
lancedb/rerankers/__pycache__/colbert.cpython-312.pyc,,
lancedb/rerankers/__pycache__/cross_encoder.cpython-312.pyc,,
lancedb/rerankers/__pycache__/jinaai.cpython-312.pyc,,
lancedb/rerankers/__pycache__/linear_combination.cpython-312.pyc,,
lancedb/rerankers/__pycache__/openai.cpython-312.pyc,,
lancedb/rerankers/__pycache__/rrf.cpython-312.pyc,,
lancedb/rerankers/__pycache__/util.cpython-312.pyc,,
lancedb/rerankers/__pycache__/voyageai.cpython-312.pyc,,
lancedb/rerankers/answerdotai.py,sha256=jVn8Ik08p3uPueTBLb9rzaCXfpd8_9UvdFzX7iwK4lE,3568
lancedb/rerankers/base.py,sha256=-75TJcvxk9dGV5ok3tkbcKOQEXcb-9d2yWL2_wypMNk,8183
lancedb/rerankers/cohere.py,sha256=3fIHdFS3o6sjX-I1Sr3Vx9xRKfC27MQWtS3gLujZSAA,4012
lancedb/rerankers/colbert.py,sha256=dc_l7kMuZlnblgQ6U9jGIp4iDYDLAHAd_r25Wh_Wocc,1154
lancedb/rerankers/cross_encoder.py,sha256=n1uzDI-U1CvWS79w7LmfPzJEuUCDIGkjYoL3SYyl57w,4142
lancedb/rerankers/jinaai.py,sha256=jRATvn06Pxa4AuCBceV_50pL_XfcCymt-L7u29Ewnh4,4008
lancedb/rerankers/linear_combination.py,sha256=HmWR-fL9GSazqX40WjquRAcmztgSLhWjtwBuYecg2ek,4844
lancedb/rerankers/openai.py,sha256=m7XE4Uv80t6mySdKmCtOYn2y7dSe-1-jawcxC_9I42o,5038
lancedb/rerankers/rrf.py,sha256=WAXwpYG5DCpHb0crKxQCStx43mPd2vTharbP2SYeTWo,4205
lancedb/rerankers/util.py,sha256=LO_5u5cCW525EtMad9nX8k8KUjcsJ6k4_JFtdYDydr8,629
lancedb/rerankers/voyageai.py,sha256=lH-QGyLHeICWl2JBVAbg6sX3k0lMYwWDXI-autqoHXI,4092
lancedb/schema.py,sha256=V5HvBQNRJvxMqQRb1B5HYE9Aw5Mva667ayLY9OVLo00,766
lancedb/table.py,sha256=pvvp4XvvVx8ThBAp4i3WH29SSfc1dmWCAipzkjpam38,155061
lancedb/types.py,sha256=aa-NNeUNAcM3C5xuKt4LMb-opy1Jzr_6R6v-FTau3xA,877
lancedb/util.py,sha256=CBogAGlnzHu1kG8ZnA5BQFmSXM9wcDXZsKcawT36Tn8,9966
