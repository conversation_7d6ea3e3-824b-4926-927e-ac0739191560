# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from chromadb.proto import query_executor_pb2 as chromadb_dot_proto_dot_query__executor__pb2


class QueryExecutorStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Count = channel.unary_unary(
                '/chroma.QueryExecutor/Count',
                request_serializer=chromadb_dot_proto_dot_query__executor__pb2.CountPlan.SerializeToString,
                response_deserializer=chromadb_dot_proto_dot_query__executor__pb2.CountResult.FromString,
                )
        self.Get = channel.unary_unary(
                '/chroma.QueryExecutor/Get',
                request_serializer=chromadb_dot_proto_dot_query__executor__pb2.GetPlan.SerializeToString,
                response_deserializer=chromadb_dot_proto_dot_query__executor__pb2.GetResult.FromString,
                )
        self.KNN = channel.unary_unary(
                '/chroma.QueryExecutor/KNN',
                request_serializer=chromadb_dot_proto_dot_query__executor__pb2.KNNPlan.SerializeToString,
                response_deserializer=chromadb_dot_proto_dot_query__executor__pb2.KNNBatchResult.FromString,
                )


class QueryExecutorServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Count(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Get(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def KNN(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_QueryExecutorServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Count': grpc.unary_unary_rpc_method_handler(
                    servicer.Count,
                    request_deserializer=chromadb_dot_proto_dot_query__executor__pb2.CountPlan.FromString,
                    response_serializer=chromadb_dot_proto_dot_query__executor__pb2.CountResult.SerializeToString,
            ),
            'Get': grpc.unary_unary_rpc_method_handler(
                    servicer.Get,
                    request_deserializer=chromadb_dot_proto_dot_query__executor__pb2.GetPlan.FromString,
                    response_serializer=chromadb_dot_proto_dot_query__executor__pb2.GetResult.SerializeToString,
            ),
            'KNN': grpc.unary_unary_rpc_method_handler(
                    servicer.KNN,
                    request_deserializer=chromadb_dot_proto_dot_query__executor__pb2.KNNPlan.FromString,
                    response_serializer=chromadb_dot_proto_dot_query__executor__pb2.KNNBatchResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'chroma.QueryExecutor', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class QueryExecutor(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Count(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/chroma.QueryExecutor/Count',
            chromadb_dot_proto_dot_query__executor__pb2.CountPlan.SerializeToString,
            chromadb_dot_proto_dot_query__executor__pb2.CountResult.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Get(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/chroma.QueryExecutor/Get',
            chromadb_dot_proto_dot_query__executor__pb2.GetPlan.SerializeToString,
            chromadb_dot_proto_dot_query__executor__pb2.GetResult.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def KNN(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/chroma.QueryExecutor/KNN',
            chromadb_dot_proto_dot_query__executor__pb2.KNNPlan.SerializeToString,
            chromadb_dot_proto_dot_query__executor__pb2.KNNBatchResult.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
