#!/usr/bin/env python3
"""
Simple Admin Panel for MCP Automated Pipeline (No Pandas Required)
A lightweight web-based dashboard to monitor the project status
"""

import os
import json
import csv
from datetime import datetime
from pathlib import Path
import subprocess
from flask import Flask, render_template_string, jsonify, request

app = Flask(__name__)

class SimplePipelineStatus:
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        
    def get_scraping_status(self):
        """Get status of data scraping without pandas"""
        try:
            scraped_file = self.base_dir / "data" / "completed_data.csv"
            
            status = {
                "scraped_data_exists": scraped_file.exists(),
                "scraped_count": 0,
                "last_updated": None
            }
            
            if scraped_file.exists():
                # Count lines without pandas
                with open(scraped_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    status["scraped_count"] = sum(1 for row in reader) - 1  # Subtract header
                status["last_updated"] = datetime.fromtimestamp(scraped_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            
            return status
        except Exception as e:
            return {"error": str(e)}
    
    def get_content_generation_status(self):
        """Get status of content generation"""
        try:
            generated_file = self.base_dir / "data" / "generated_content.json"
            
            status = {
                "generated_content_exists": generated_file.exists(),
                "generated_count": 0,
                "last_updated": None,
                "file_size": 0
            }
            
            if generated_file.exists():
                with open(generated_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    status["generated_count"] = len(data)
                    status["file_size"] = round(generated_file.stat().st_size / 1024 / 1024, 2)  # MB
                    status["last_updated"] = datetime.fromtimestamp(generated_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            
            return status
        except Exception as e:
            return {"error": str(e)}
    
    def get_system_status(self):
        """Get overall system status"""
        try:
            required_dirs = ["data", "logs", "scrapper", "content_processor"]
            dir_status = {}
            
            for dir_name in required_dirs:
                dir_path = self.base_dir / dir_name
                dir_status[dir_name] = dir_path.exists()
            
            log_dir = self.base_dir / "logs"
            log_files = []
            if log_dir.exists():
                log_files = [f.name for f in log_dir.glob("*.log")]
            
            return {
                "directories": dir_status,
                "log_files": log_files,
                "base_dir_exists": self.base_dir.exists()
            }
        except Exception as e:
            return {"error": str(e)}

    def get_sample_data(self):
        """Get sample data from files"""
        try:
            sample_data = []
            
            # Try to read some sample data from completed_data.csv
            completed_file = self.base_dir / "data" / "completed_data.csv"
            if completed_file.exists():
                with open(completed_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for i, row in enumerate(reader):
                        if i >= 5:  # Only show first 5 rows
                            break
                        sample_data.append(dict(row))
            
            return sample_data
        except Exception as e:
            return [{"error": str(e)}]

pipeline_status = SimplePipelineStatus()

# Simple HTML template
DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>MCP Pipeline Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .status-item { padding: 10px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .data-table { width: 100%; border-collapse: collapse; }
        .data-table th, .data-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        .data-table th { background: #f8f9fa; }
        h1, h2 { color: #333; }
        .refresh-btn { float: right; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 MCP Automated Pipeline Dashboard</h1>
        <button class="btn refresh-btn" onclick="location.reload()">🔄 Refresh</button>
        
        <div class="status-grid">
            <div class="card">
                <h2>📊 Scraping Status</h2>
                <div id="scraping-status">Loading...</div>
            </div>
            
            <div class="card">
                <h2>🤖 Content Generation</h2>
                <div id="content-status">Loading...</div>
            </div>
            
            <div class="card">
                <h2>⚙️ System Status</h2>
                <div id="system-status">Loading...</div>
            </div>
        </div>
        
        <div class="card">
            <h2>🎮 Quick Actions</h2>
            <button class="btn" onclick="runScraping()">🔍 Run Scraping</button>
            <button class="btn" onclick="runContentGeneration()">📝 Generate Content</button>
            <button class="btn" onclick="viewLogs()">📋 View Logs</button>
        </div>
        
        <div class="card">
            <h2>📋 Sample Data</h2>
            <div id="sample-data">Loading...</div>
        </div>
    </div>

    <script>
        function loadStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('scraping-status').innerHTML = formatScrapingStatus(data.scraping);
                    document.getElementById('content-status').innerHTML = formatContentStatus(data.content_generation);
                    document.getElementById('system-status').innerHTML = formatSystemStatus(data.system);
                });
            
            fetch('/api/sample-data')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('sample-data').innerHTML = formatSampleData(data.data);
                });
        }
        
        function formatScrapingStatus(status) {
            if (status.error) return `<div class="status-item error">Error: ${status.error}</div>`;
            return `
                <div class="status-item ${status.scraped_data_exists ? 'success' : 'warning'}">
                    <strong>Data File:</strong> ${status.scraped_data_exists ? 'Exists' : 'Missing'}<br>
                    <strong>Records:</strong> ${status.scraped_count}<br>
                    <strong>Last Updated:</strong> ${status.last_updated || 'Never'}
                </div>
            `;
        }
        
        function formatContentStatus(status) {
            if (status.error) return `<div class="status-item error">Error: ${status.error}</div>`;
            return `
                <div class="status-item ${status.generated_content_exists ? 'success' : 'warning'}">
                    <strong>Generated Content:</strong> ${status.generated_content_exists ? 'Exists' : 'Missing'}<br>
                    <strong>Records:</strong> ${status.generated_count}<br>
                    <strong>File Size:</strong> ${status.file_size} MB<br>
                    <strong>Last Updated:</strong> ${status.last_updated || 'Never'}
                </div>
            `;
        }
        
        function formatSystemStatus(status) {
            if (status.error) return `<div class="status-item error">Error: ${status.error}</div>`;
            let html = '<div class="status-item">';
            for (const [dir, exists] of Object.entries(status.directories)) {
                html += `<strong>${dir}:</strong> ${exists ? '✅' : '❌'}<br>`;
            }
            html += `<strong>Log Files:</strong> ${status.log_files.length}</div>`;
            return html;
        }
        
        function formatSampleData(data) {
            if (!data || data.length === 0) return '<p>No data available</p>';
            if (data[0].error) return `<p>Error: ${data[0].error}</p>`;
            
            let html = '<table class="data-table"><thead><tr>';
            const headers = Object.keys(data[0]).slice(0, 5); // Show first 5 columns
            headers.forEach(header => html += `<th>${header}</th>`);
            html += '</tr></thead><tbody>';
            
            data.forEach(row => {
                html += '<tr>';
                headers.forEach(header => {
                    const value = row[header] || '';
                    const truncated = value.length > 50 ? value.substring(0, 50) + '...' : value;
                    html += `<td>${truncated}</td>`;
                });
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            return html;
        }
        
        function runScraping() {
            alert('Scraping feature will be implemented. For now, run: python scrapper/scripts/main.py --automated');
        }
        
        function runContentGeneration() {
            alert('Content generation feature will be implemented. For now, run: python run_content_generation.py');
        }
        
        function viewLogs() {
            alert('Log viewing feature will be implemented. For now, check the logs/ directory');
        }
        
        // Load status on page load
        loadStatus();
        
        // Auto-refresh every 30 seconds
        setInterval(loadStatus, 30000);
    </script>
</body>
</html>
"""

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template_string(DASHBOARD_TEMPLATE)

@app.route('/api/status')
def api_status():
    """API endpoint for status data"""
    return jsonify({
        "scraping": pipeline_status.get_scraping_status(),
        "content_generation": pipeline_status.get_content_generation_status(),
        "system": pipeline_status.get_system_status(),
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

@app.route('/api/sample-data')
def api_sample_data():
    """API endpoint for sample data"""
    return jsonify({
        "data": pipeline_status.get_sample_data(),
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

if __name__ == '__main__':
    print("🚀 Starting Simple MCP Pipeline Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:8080")
    print("🔄 Auto-refresh every 30 seconds")
    print("⚠️  Note: This is a simplified version without pandas dependency")
    app.run(debug=True, host='0.0.0.0', port=8080)
