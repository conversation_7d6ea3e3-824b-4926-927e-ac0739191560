# Enhanced Automation System Requirements
# Fixed dependency versions to avoid conflicts (Updated June 2025)

# Core data processing dependencies
pandas>=2.0.0,<3.0.0
numpy>=1.21.0,<2.0.0
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0

# Web framework for admin panel
flask>=3.0.0
python-dotenv>=1.0.0

# SEO and text processing
python-slugify>=8.0.0

# Event-driven architecture (optional)
redis>=4.5.0
schedule>=1.2.0

# Enhanced content processing with CrewAI (latest stable versions)
# Note: Install these separately if needed to avoid conflicts
# crewai>=0.130.0
# openai>=1.0.0

# Optional: Additional web frameworks
# fastapi>=0.95.0
# uvicorn>=0.20.0

# Optional: Async support
# aiohttp>=3.8.0

# Optional: Monitoring
# prometheus-client>=0.16.0

# Development and testing (optional)
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
